package api

import (
	"errors"
	"testing"

	"github.com/jackc/pgerrcode"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/koopa0/pms-api-v2/internal/platform/database"
	"github.com/stretchr/testify/assert"
)

func TestGetSpecificErrorMessage(t *testing.T) {
	tests := []struct {
		name      string
		err       error
		operation string
		expected  string
	}{
		{
			name:      "nil error",
			err:       nil,
			operation: "create",
			expected:  "",
		},
		{
			name: "database error",
			err: &database.DBError{
				Type:    database.ErrorTypeNotFound,
				Message: "get user: record not found",
			},
			operation: "查詢使用者",
			expected:  "查詢使用者失敗：找不到相關資料",
		},
		{
			name:      "generic error",
			err:       errors.New("some error"),
			operation: "操作",
			expected:  "操作失敗，請稍後再試",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetSpecificErrorMessage(tt.err, tt.operation)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestHandlePostgresError(t *testing.T) {
	tests := []struct {
		name     string
		pgErr    *pgconn.PgError
		operation string
		expected string
	}{
		{
			name: "unique violation with detail",
			pgErr: &pgconn.PgError{
				Code:   pgerrcode.UniqueViolation,
				Detail: "Key (username)=(test) already exists.",
			},
			operation: "建立使用者",
			expected:  "建立使用者失敗：資料已存在（重複的 使用者名稱）",
		},
		{
			name: "unique violation without detail",
			pgErr: &pgconn.PgError{
				Code:   pgerrcode.UniqueViolation,
				Detail: "",
			},
			operation: "建立",
			expected:  "建立失敗：資料已存在（重複的 資料）",
		},
		{
			name: "foreign key violation",
			pgErr: &pgconn.PgError{
				Code: pgerrcode.ForeignKeyViolation,
			},
			operation: "刪除",
			expected:  "刪除失敗：相關資料不存在或無法刪除（有其他資料依賴此項目）",
		},
		{
			name: "not null violation",
			pgErr: &pgconn.PgError{
				Code:       pgerrcode.NotNullViolation,
				ColumnName: "email",
			},
			operation: "建立使用者",
			expected:  "建立使用者失敗：必填欄位 電子郵件 不能為空",
		},
		{
			name: "check violation",
			pgErr: &pgconn.PgError{
				Code:           pgerrcode.CheckViolation,
				ConstraintName: "positive_price",
			},
			operation: "更新價格",
			expected:  "更新價格失敗：資料不符合規定（positive_price）",
		},
		{
			name: "invalid text representation",
			pgErr: &pgconn.PgError{
				Code: pgerrcode.InvalidTextRepresentation,
			},
			operation: "建立",
			expected:  "建立失敗：輸入格式錯誤",
		},
		{
			name: "undefined table",
			pgErr: &pgconn.PgError{
				Code: pgerrcode.UndefinedTable,
			},
			operation: "查詢",
			expected:  "系統設定錯誤，請聯絡系統管理員",
		},
		{
			name: "undefined column",
			pgErr: &pgconn.PgError{
				Code: pgerrcode.UndefinedColumn,
			},
			operation: "查詢",
			expected:  "系統設定錯誤，請聯絡系統管理員",
		},
		{
			name: "query canceled",
			pgErr: &pgconn.PgError{
				Code: pgerrcode.QueryCanceled,
			},
			operation: "查詢",
			expected:  "查詢已取消：操作時間過長",
		},
		{
			name: "too many connections",
			pgErr: &pgconn.PgError{
				Code: pgerrcode.TooManyConnections,
			},
			operation: "連線",
			expected:  "系統繁忙，請稍後再試",
		},
		{
			name: "connection failure",
			pgErr: &pgconn.PgError{
				Code: pgerrcode.ConnectionFailure,
			},
			operation: "連線",
			expected:  "資料庫連線失敗，請稍後再試",
		},
		{
			name: "deadlock detected",
			pgErr: &pgconn.PgError{
				Code: pgerrcode.DeadlockDetected,
			},
			operation: "更新",
			expected:  "更新失敗：資源衝突，請稍後再試",
		},
		{
			name: "serialization failure",
			pgErr: &pgconn.PgError{
				Code: pgerrcode.SerializationFailure,
			},
			operation: "更新",
			expected:  "更新失敗：交易衝突，請重新嘗試",
		},
		{
			name: "lock not available",
			pgErr: &pgconn.PgError{
				Code: pgerrcode.LockNotAvailable,
			},
			operation: "更新",
			expected:  "更新失敗：等待逾時，請稍後再試",
		},
		{
			name: "unknown error code",
			pgErr: &pgconn.PgError{
				Code: "99999",
			},
			operation: "操作",
			expected:  "操作失敗：資料庫錯誤 99999",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := handlePostgresError(tt.pgErr, tt.operation)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestExtractFieldName(t *testing.T) {
	tests := []struct {
		name     string
		detail   string
		expected string
	}{
		{
			name:     "standard PostgreSQL detail with username",
			detail:   "Key (username)=(test) already exists.",
			expected: "使用者名稱",
		},
		{
			name:     "detail with email field",
			detail:   "Key (email)=(<EMAIL>) already exists.",
			expected: "電子郵件",
		},
		{
			name:     "detail with unified_business_no",
			detail:   "Key (unified_business_no)=(12345678) already exists.",
			expected: "統一編號",
		},
		{
			name:     "detail without parentheses",
			detail:   "Something went wrong",
			expected: "資料",
		},
		{
			name:     "empty detail",
			detail:   "",
			expected: "資料",
		},
		{
			name:     "malformed detail",
			detail:   "Key (username",
			expected: "資料",
		},
		{
			name:     "empty field in parentheses",
			detail:   "Key ()=() already exists.",
			expected: "資料",
		},
		{
			name:     "unknown field",
			detail:   "Key (unknown_field)=(value) already exists.",
			expected: "unknown_field",
		},
		{
			name:     "detail with multiple parentheses",
			detail:   "Key (username)=(test) (more info) already exists.",
			expected: "使用者名稱",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := extractFieldName(tt.detail)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestGetErrorCode(t *testing.T) {
	tests := []struct {
		name string
		err  error
	}{
		{
			name: "simple error",
			err:  errors.New("test error"),
		},
		{
			name: "database error",
			err:  &database.DBError{Type: database.ErrorTypeNotFound, Message: "get: record not found"},
		},
		{
			name: "postgres error",
			err:  &pgconn.PgError{Code: pgerrcode.UniqueViolation},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			code := GetErrorCode(tt.err)
			
			// Error code should start with "ERR-"
			assert.True(t, len(code) > 4)
			assert.Equal(t, "ERR-", code[:4])
			
			// Should be deterministic - same error should produce same code
			code2 := GetErrorCode(tt.err)
			assert.Equal(t, code, code2)
			
			// Should be 4-digit number after ERR-
			assert.Len(t, code, 8) // "ERR-" + 4 digits
		})
	}
}

func TestHashError(t *testing.T) {
	tests := []struct {
		name string
		err  error
	}{
		{
			name: "simple error",
			err:  errors.New("test error"),
		},
		{
			name: "empty error",
			err:  errors.New(""),
		},
		{
			name: "long error message",
			err:  errors.New("this is a very long error message with many characters that should still hash consistently"),
		},
		{
			name: "unicode error",
			err:  errors.New("錯誤訊息包含中文字元"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			hash := hashError(tt.err)
			
			// Hash should be within expected range (0-9999)
			assert.True(t, hash >= 0)
			assert.True(t, hash <= 9999)
			
			// Should be deterministic
			hash2 := hashError(tt.err)
			assert.Equal(t, hash, hash2)
		})
	}
}

// Test that different errors produce different hashes (collision resistance)
func TestHashErrorCollisionResistance(t *testing.T) {
	errors := []error{
		errors.New("error 1"),
		errors.New("error 2"),
		errors.New("error 3"),
		errors.New("different error"),
		errors.New("another different error"),
	}

	hashes := make(map[uint32]bool)
	for _, err := range errors {
		hash := hashError(err)
		// Note: We don't assert no collisions because with only 10000 possible values,
		// collisions are expected. This test just ensures the function works.
		hashes[hash] = true
	}

	// At least some different hashes should be generated
	assert.True(t, len(hashes) > 1, "Hash function should generate different hashes for different inputs")
}

// Benchmark tests
func BenchmarkGetSpecificErrorMessage(b *testing.B) {
	err := &database.DBError{
		Type:      database.ErrorTypeDuplicate,
		Message: "create: operation failed",
		Detail:    "Key (username)=(test) already exists.",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = GetSpecificErrorMessage(err, "建立使用者")
	}
}

func BenchmarkHandlePostgresError(b *testing.B) {
	pgErr := &pgconn.PgError{
		Code:   pgerrcode.UniqueViolation,
		Detail: "Key (username)=(test) already exists.",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = handlePostgresError(pgErr, "建立使用者")
	}
}

func BenchmarkExtractFieldName(b *testing.B) {
	detail := "Key (username)=(test) already exists."

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = extractFieldName(detail)
	}
}

func BenchmarkGetErrorCode(b *testing.B) {
	err := errors.New("test error message")

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = GetErrorCode(err)
	}
}

func BenchmarkHashError(b *testing.B) {
	err := errors.New("test error message for benchmarking")

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = hashError(err)
	}
}

// Fuzzing tests
func FuzzExtractFieldName(f *testing.F) {
	// Add seed corpus
	f.Add("Key (username)=(test) already exists.")
	f.Add("Key (email)=(<EMAIL>) already exists.")
	f.Add("No parentheses here")
	f.Add("")
	f.Add("Key (")
	f.Add("Key () already exists.")
	f.Add("Key (field_name)=(value) more text")

	f.Fuzz(func(t *testing.T, detail string) {
		result := extractFieldName(detail)
		// Property: function should not panic
		// Property: result should never be empty
		assert.NotEmpty(t, result)
		// Property: result should be deterministic
		result2 := extractFieldName(detail)
		assert.Equal(t, result, result2)
	})
}

func FuzzHashError(f *testing.F) {
	// Add seed corpus
	f.Add("simple error")
	f.Add("")
	f.Add("error with special chars !@#$%^&*()")
	f.Add("錯誤訊息")
	f.Add("very long error message that contains many words and characters to test the hash function behavior")

	f.Fuzz(func(t *testing.T, errMsg string) {
		err := errors.New(errMsg)
		hash := hashError(err)
		// Property: function should not panic
		// Property: hash should be in valid range
		assert.True(t, hash >= 0)
		assert.True(t, hash <= 9999)
		// Property: hash should be deterministic
		hash2 := hashError(err)
		assert.Equal(t, hash, hash2)
	})
}

func FuzzGetErrorCode(f *testing.F) {
	// Add seed corpus
	f.Add("test error")
	f.Add("")
	f.Add("database connection failed")
	f.Add("錯誤訊息")
	f.Add("unicode error: ñoño")

	f.Fuzz(func(t *testing.T, errMsg string) {
		err := errors.New(errMsg)
		code := GetErrorCode(err)
		// Property: function should not panic
		// Property: code should have correct format
		assert.True(t, len(code) == 8) // "ERR-" + 4 digits
		assert.Equal(t, "ERR-", code[:4])
		// Property: code should be deterministic
		code2 := GetErrorCode(err)
		assert.Equal(t, code, code2)
	})
}