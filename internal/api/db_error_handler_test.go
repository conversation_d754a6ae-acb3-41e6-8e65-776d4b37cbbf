package api

import (
	"errors"
	"testing"

	"github.com/koopa0/pms-api-v2/internal/platform/database"
	"github.com/stretchr/testify/assert"
)

func TestDBErrorMessage(t *testing.T) {
	tests := []struct {
		name      string
		err       error
		operation string
		expected  string
	}{
		{
			name:      "nil error",
			err:       nil,
			operation: "create",
			expected:  "",
		},
		{
			name: "not found error",
			err: &database.DBError{
				Type:    database.ErrorTypeNotFound,
				Message: "get user: record not found",
			},
			operation: "查詢使用者",
			expected:  "查詢使用者失敗：找不到相關資料",
		},
		{
			name: "duplicate error with field",
			err: &database.DBError{
				Type:    database.ErrorTypeDuplicate,
				Message: "create user: duplicate key violation",
				Detail:  "Key (username)=(test) already exists.",
			},
			operation: "建立使用者",
			expected:  "建立使用者失敗：使用者名稱已存在",
		},
		{
			name: "duplicate error without field",
			err: &database.DBError{
				Type:    database.ErrorTypeDuplicate,
				Message: "create: duplicate key violation",
			},
			operation: "建立",
			expected:  "建立失敗：資料已存在",
		},
		{
			name: "foreign key error for delete",
			err: &database.DBError{
				Type:    database.ErrorTypeForeignKey,
				Message: "delete: foreign key constraint violation",
			},
			operation: "刪除專案",
			expected:  "刪除專案失敗：此項目有其他相關資料，無法刪除",
		},
		{
			name: "foreign key error for create",
			err: &database.DBError{
				Type:    database.ErrorTypeForeignKey,
				Message: "create: foreign key constraint violation",
			},
			operation: "建立報價",
			expected:  "建立報價失敗：相關資料不存在",
		},
		{
			name: "not null error with column",
			err: &database.DBError{
				Type:    database.ErrorTypeNotNull,
				Message: "create: null value not allowed",
				Column:  "email",
			},
			operation: "建立使用者",
			expected:  "建立使用者失敗：電子郵件為必填欄位",
		},
		{
			name: "check constraint error",
			err: &database.DBError{
				Type:       database.ErrorTypeCheckConstraint,
				Message:    "update: check constraint violation",
				Constraint: "positive_price",
			},
			operation: "更新價格",
			expected:  "更新價格失敗：數值必須大於0",
		},
		{
			name: "invalid input error",
			err: &database.DBError{
				Type:    database.ErrorTypeInvalidInput,
				Message: "create: invalid input format or value",
			},
			operation: "建立",
			expected:  "建立失敗：輸入資料格式錯誤",
		},
		{
			name: "timeout error",
			err: &database.DBError{
				Type:    database.ErrorTypeTimeout,
				Message: "query: operation timed out",
			},
			operation: "查詢",
			expected:  "查詢逾時：操作時間過長，請稍後再試",
		},
		{
			name: "connection failed error",
			err: &database.DBError{
				Type:    database.ErrorTypeConnectionFailed,
				Message: "connect: database connection failed",
			},
			operation: "連線",
			expected:  "無法連線到資料庫，請聯絡系統管理員",
		},
		{
			name: "too many connections error",
			err: &database.DBError{
				Type:    database.ErrorTypeTooManyConnections,
				Message: "connect: too many database connections",
			},
			operation: "連線",
			expected:  "系統繁忙，請稍後再試",
		},
		{
			name: "permission denied error",
			err: &database.DBError{
				Type:    database.ErrorTypePermissionDenied,
				Message: "delete: permission denied",
			},
			operation: "刪除",
			expected:  "刪除失敗：權限不足",
		},
		{
			name: "deadlock error",
			err: &database.DBError{
				Type:    database.ErrorTypeDeadlock,
				Message: "update: deadlock detected",
			},
			operation: "更新",
			expected:  "更新失敗：資源衝突，請稍後再試",
		},
		{
			name: "lock timeout error",
			err: &database.DBError{
				Type:    database.ErrorTypeLockTimeout,
				Message: "update: lock timeout",
			},
			operation: "更新",
			expected:  "更新失敗：等待逾時，請稍後再試",
		},
		{
			name: "serialization failure error",
			err: &database.DBError{
				Type:    database.ErrorTypeSerializationFailure,
				Message: "update: serialization failure, please retry",
			},
			operation: "更新",
			expected:  "更新失敗：交易衝突，請重新嘗試",
		},
		{
			name: "unknown error type",
			err: &database.DBError{
				Type:    database.ErrorType(999),
				Message: "process: unknown error",
			},
			operation: "處理",
			expected:  "處理失敗，請稍後再試",
		},
		{
			name:      "non-database error",
			err:       errors.New("some random error"),
			operation: "操作",
			expected:  "操作失敗，請稍後再試",
		},
		{
			name:      "wrapped non-database error",
			err:       errors.New("connection refused"),
			operation: "create",
			expected:  "無法連線到資料庫，請聯絡系統管理員",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := DBErrorMessage(tt.err, tt.operation)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestTranslateOperation(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{"exact match lowercase", "create", "建立"},
		{"exact match uppercase", "CREATE", "建立"},
		{"exact match mixed case", "Update", "更新"},
		{"contains English term", "create_user", "建立_user"},
		{"multiple terms", "create and update", "建立 and update"},
		{"no translation", "unknown_operation", "unknown_operation"},
		{"empty string", "", ""},
		{"login operation", "login", "登入"},
		{"register operation", "register", "註冊"},
		{"approve operation", "approve", "核准"},
		{"reject operation", "reject", "退件"},
		{"upload operation", "upload", "上傳"},
		{"download operation", "download", "下載"},
		{"import operation", "import", "匯入"},
		{"export operation", "export", "匯出"},
		{"validate operation", "validate", "驗證"},
		{"archive operation", "archive", "封存"},
		{"restore operation", "restore", "還原"},
		{"grant operation", "grant", "授權"},
		{"revoke operation", "revoke", "撤銷"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := translateOperation(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestExtractFieldFromDetail(t *testing.T) {
	tests := []struct {
		name     string
		detail   string
		expected string
	}{
		{
			name:     "standard PostgreSQL duplicate key error",
			detail:   "Key (username)=(test) already exists.",
			expected: "username",
		},
		{
			name:     "field with underscore",
			detail:   "Key (unified_business_no)=(12345678) already exists.",
			expected: "unified_business_no",
		},
		{
			name:     "no parentheses",
			detail:   "Something went wrong",
			expected: "",
		},
		{
			name:     "empty detail",
			detail:   "",
			expected: "",
		},
		{
			name:     "malformed parentheses",
			detail:   "Key (username",
			expected: "",
		},
		{
			name:     "empty parentheses",
			detail:   "Key ()=() already exists.",
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := extractFieldFromDetail(tt.detail)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestTranslateConstraint(t *testing.T) {
	tests := []struct {
		name       string
		constraint string
		expected   string
	}{
		{"positive constraint", "positive_price", "數值必須大於0"},
		{"non_negative constraint", "non_negative_amount", "數值不能為負數"},
		{"percentage constraint", "valid_percentage", "百分比必須在0到100之間"},
		{"email constraint", "valid_email", "電子郵件格式不正確"},
		{"phone constraint", "valid_phone", "電話號碼格式不正確"},
		{"date_range constraint", "valid_date_range", "日期範圍不正確"},
		{"future_date constraint", "must_be_future_date", "日期必須是未來時間"},
		{"past_date constraint", "must_be_past_date", "日期必須是過去時間"},
		{"valid_status constraint", "valid_status_check", "狀態值不正確"},
		{"valid_role constraint", "valid_role_check", "角色值不正確"},
		{"min_length constraint", "min_length_5", "長度太短"},
		{"max_length constraint", "max_length_255", "長度太長"},
		{"unique constraint", "unique_email", "電子郵件格式不正確"},
		{"not_empty constraint", "not_empty_name", "不能為空"},
		{"unknown constraint", "some_custom_constraint", "違反限制條件：some_custom_constraint"},
		{"empty constraint", "", "資料不符合規定"},
		{"case insensitive", "POSITIVE_VALUE", "數值必須大於0"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := translateConstraint(tt.constraint)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestDBHelperFunctions(t *testing.T) {
	t.Run("CreateErrorMessage", func(t *testing.T) {
		err := &database.DBError{
			Type:    database.ErrorTypeDuplicate,
			Message: "create: duplicate key violation",
			Detail:  "Key (email)=(<EMAIL>) already exists.",
		}
		result := CreateErrorMessage(err, "使用者")
		assert.Equal(t, "建立使用者失敗：電子郵件已存在", result)
	})

	t.Run("UpdateErrorMessage", func(t *testing.T) {
		err := &database.DBError{
			Type:    database.ErrorTypeNotFound,
			Message: "update: record not found",
		}
		result := UpdateErrorMessage(err, "專案")
		assert.Equal(t, "更新專案失敗：找不到相關資料", result)
	})

	t.Run("DeleteErrorMessage", func(t *testing.T) {
		err := &database.DBError{
			Type:    database.ErrorTypeForeignKey,
			Message: "delete: foreign key constraint violation",
		}
		result := DeleteErrorMessage(err, "公司")
		assert.Equal(t, "刪除公司失敗：此項目有其他相關資料，無法刪除", result)
	})

	t.Run("GetErrorMessage", func(t *testing.T) {
		err := &database.DBError{
			Type:    database.ErrorTypeTimeout,
			Message: "get: operation timed out",
		}
		result := GetErrorMessage(err, "報價")
		assert.Equal(t, "查詢報價逾時：操作時間過長，請稍後再試", result)
	})

	t.Run("ListErrorMessage", func(t *testing.T) {
		err := &database.DBError{
			Type:    database.ErrorTypePermissionDenied,
			Message: "list: permission denied",
		}
		result := ListErrorMessage(err, "產品")
		assert.Equal(t, "列出產品失敗：權限不足", result)
	})
}

func TestTranslateFieldName(t *testing.T) {
	tests := []struct {
		name     string
		field    string
		expected string
	}{
		{"username field", "username", "使用者名稱"},
		{"email field", "email", "電子郵件"},
		{"unified_business_no field", "unified_business_no", "統一編號"},
		{"name field", "name", "名稱"},
		{"pid field", "pid", "產品編號"},
		{"project_id field", "project_id", "專案"},
		{"product_id field", "product_id", "產品"},
		{"user_id field", "user_id", "使用者"},
		{"company_id field", "company_id", "公司"},
		{"backup_email field", "backup_email", "備用電子郵件"},
		{"job_title field", "job_title", "職稱"},
		{"mobile field", "mobile", "手機號碼"},
		{"company_name field", "company_name", "公司名稱"},
		{"password field", "password", "密碼"},
		{"password_hash field", "password_hash", "密碼"},
		{"role field", "role", "角色"},
		{"status field", "status", "狀態"},
		{"created_at field", "created_at", "建立時間"},
		{"updated_at field", "updated_at", "更新時間"},
		{"unknown field", "unknown_field", "unknown_field"},
		{"empty field", "", ""},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := translateFieldName(tt.field)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// Benchmark tests
func BenchmarkDBErrorMessage(b *testing.B) {
	err := &database.DBError{
		Type:      database.ErrorTypeDuplicate,
		Message: "create: duplicate key violation",
		Detail:    "Key (username)=(test) already exists.",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = DBErrorMessage(err, "建立使用者")
	}
}

func BenchmarkTranslateOperation(b *testing.B) {
	operations := []string{"create", "update", "delete", "get", "list"}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = translateOperation(operations[i%len(operations)])
	}
}

func BenchmarkExtractFieldFromDetail(b *testing.B) {
	detail := "Key (username)=(test) already exists."
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = extractFieldFromDetail(detail)
	}
}

// Fuzzing tests
func FuzzTranslateOperation(f *testing.F) {
	// Add seed corpus
	f.Add("create")
	f.Add("UPDATE")
	f.Add("delete_user")
	f.Add("get/list")
	f.Add("")
	f.Add("建立")
	f.Add("unknown_op_123")
	
	f.Fuzz(func(t *testing.T, operation string) {
		result := translateOperation(operation)
		// Property: function should not panic
		// Property: result should not be empty if input is not empty
		if operation != "" {
			assert.NotEmpty(t, result)
		}
		// Property: result should be deterministic
		result2 := translateOperation(operation)
		assert.Equal(t, result, result2)
	})
}

func FuzzExtractFieldFromDetail(f *testing.F) {
	// Add seed corpus
	f.Add("Key (username)=(test) already exists.")
	f.Add("Key (email)=(<EMAIL>) already exists.")
	f.Add("Some other error message")
	f.Add("")
	f.Add("Key (")
	f.Add("Key () already exists.")
	
	f.Fuzz(func(t *testing.T, detail string) {
		result := extractFieldFromDetail(detail)
		// Property: function should not panic
		// Property: result should be empty or a valid field name
		if result != "" {
			assert.NotContains(t, result, "(")
			assert.NotContains(t, result, ")")
		}
		// Property: result should be deterministic
		result2 := extractFieldFromDetail(detail)
		assert.Equal(t, result, result2)
	})
}

func FuzzTranslateConstraint(f *testing.F) {
	// Add seed corpus
	f.Add("positive_price")
	f.Add("VALID_EMAIL")
	f.Add("min_length_10")
	f.Add("")
	f.Add("unknown_constraint_123")
	f.Add("非常長的約束名稱_with_mixed_語言")
	
	f.Fuzz(func(t *testing.T, constraint string) {
		result := translateConstraint(constraint)
		// Property: function should not panic
		// Property: result should always have content
		assert.NotEmpty(t, result)
		// Property: result should be deterministic
		result2 := translateConstraint(constraint)
		assert.Equal(t, result, result2)
	})
}