package api

import (
	"errors"
	"fmt"
)

// Common error codes for structured API responses
const (
	ErrCodeNotFound           = "NOT_FOUND"
	ErrCodeUnauthorized       = "UNAUTHORIZED"
	ErrCodeForbidden          = "FORBIDDEN"
	ErrCodeBadRequest         = "BAD_REQUEST"
	ErrCodeConflict           = "CONFLICT"
	ErrCodeInternalError      = "INTERNAL_ERROR"
	ErrCodeValidation         = "VALIDATION_ERROR"
	ErrCodeDuplicateEntry     = "DUPLICATE_ENTRY"
	ErrCodeNotInTimeWindow    = "NOT_IN_TIME_WINDOW"
	ErrCodeQuoteAlreadyExists = "QUOTE_ALREADY_EXISTS"
	ErrCodePasswordExpired    = "PASSWORD_EXPIRED"
	ErrCodeInvalidCredentials = "INVALID_CREDENTIALS"
	ErrCodeAccountLocked      = "ACCOUNT_LOCKED"
	ErrCodeAccountPending     = "ACCOUNT_PENDING"
	ErrCodeUserNotRegistered  = "USER_NOT_REGISTERED"
	ErrCodePasswordIncorrect  = "PASSWORD_INCORRECT"
	ErrCodeAccountNotApproved = "ACCOUNT_NOT_APPROVED"
	ErrCodeAccountRejected    = "ACCOUNT_REJECTED"
	ErrCodeInvalidInput       = "INVALID_INPUT"
	ErrCodeTooManyRequests    = "TOO_MANY_REQUESTS"
	ErrCodeServiceUnavailable = "SERVICE_UNAVAILABLE"
	ErrCodeTimeout            = "TIMEOUT"
)

// Common errors with clear Chinese messages
var (
	ErrNotFound           = errors.New("找不到請求的資源")
	ErrUnauthorized       = errors.New("請先登入系統")
	ErrForbidden          = errors.New("您沒有權限執行此操作")
	ErrBadRequest         = errors.New("請求格式錯誤，請檢查輸入資料")
	ErrInvalidInput       = errors.New("輸入資料格式不正確")
	ErrDuplicateEntry     = errors.New("資料已存在，請勿重複建立")
	ErrNotInTimeWindow    = errors.New("目前不在允許的操作時間範圍內")
	ErrQuoteAlreadyExists = errors.New("此產品已經有報價資料，請勿重複報價")
	ErrPasswordExpired    = errors.New("密碼已過期，請重設密碼")
	ErrInvalidCredentials = errors.New("帳號或密碼錯誤")
	ErrAccountLocked      = errors.New("帳號已被鎖定，請聯絡系統管理員")
	ErrAccountPending     = errors.New("帳號審核中，請等待管理員審核")
	ErrInternal           = errors.New("系統處理錯誤，請稍後再試")
	ErrUserNotRegistered  = errors.New("此電子郵件尚未註冊，請先註冊帳號")
	ErrPasswordIncorrect  = errors.New("密碼錯誤，請重新輸入")
	ErrAccountNotApproved = errors.New("帳號尚未通過審核，無法登入")
	ErrAccountRejected    = errors.New("帳號審核未通過，無法登入")
	ErrServiceUnavailable = errors.New("服務暫時無法使用，請稍後再試")
	ErrTimeout            = errors.New("請求逾時，請重新嘗試")
	ErrTooManyRequests    = errors.New("請求次數過多，請稍後再試")
)

// ErrorCategory represents different types of errors for better handling
type ErrorCategory string

const (
	CategoryValidation     ErrorCategory = "validation"
	CategoryAuthentication ErrorCategory = "authentication"
	CategoryAuthorization  ErrorCategory = "authorization"
	CategoryNotFound       ErrorCategory = "not_found"
	CategoryConflict       ErrorCategory = "conflict"
	CategoryInternal       ErrorCategory = "internal"
	CategoryExternal       ErrorCategory = "external"
)

// AppError represents a structured application error
type AppError struct {
	Code     string        `json:"code"`
	Message  string        `json:"message"`
	Category ErrorCategory `json:"category"`
	Details  any           `json:"details,omitempty"`
	Cause    error         `json:"-"`
}

// Error implements the error interface
func (e *AppError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("%s: %v", e.Message, e.Cause)
	}
	return e.Message
}

// Unwrap returns the underlying error for error wrapping support
func (e *AppError) Unwrap() error {
	return e.Cause
}

// NewValidationError creates a validation error with field details
func NewValidationError(message string, details any) *AppError {
	return &AppError{
		Code:     ErrCodeValidation,
		Message:  message,
		Category: CategoryValidation,
		Details:  details,
	}
}

// NewAuthenticationError creates an authentication error
func NewAuthenticationError(message string) *AppError {
	return &AppError{
		Code:     ErrCodeUnauthorized,
		Message:  message,
		Category: CategoryAuthentication,
	}
}

// NewAuthorizationError creates an authorization error
func NewAuthorizationError(message string) *AppError {
	return &AppError{
		Code:     ErrCodeForbidden,
		Message:  message,
		Category: CategoryAuthorization,
	}
}

// NewNotFoundError creates a not found error
func NewNotFoundError(message string) *AppError {
	return &AppError{
		Code:     ErrCodeNotFound,
		Message:  message,
		Category: CategoryNotFound,
	}
}

// NewConflictError creates a conflict error
func NewConflictError(message string, details any) *AppError {
	return &AppError{
		Code:     ErrCodeConflict,
		Message:  message,
		Category: CategoryConflict,
		Details:  details,
	}
}

// NewInternalError creates an internal server error
func NewInternalError(message string, cause error) *AppError {
	return &AppError{
		Code:     ErrCodeInternalError,
		Message:  message,
		Category: CategoryInternal,
		Cause:    cause,
	}
}
