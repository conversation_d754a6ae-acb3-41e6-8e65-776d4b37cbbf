// Package server 提供 HTTP 伺服器實現
// 負責伺服器初始化、路由設定和生命週期管理
package server

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/koopa0/pms-api-v2/internal/api"
	"github.com/koopa0/pms-api-v2/internal/business/auth"
	"github.com/koopa0/pms-api-v2/internal/business/product"
	"github.com/koopa0/pms-api-v2/internal/business/project"
	"github.com/koopa0/pms-api-v2/internal/business/quote"
	"github.com/koopa0/pms-api-v2/internal/business/registration"
	"github.com/koopa0/pms-api-v2/internal/business/user"
	"github.com/koopa0/pms-api-v2/internal/platform/config"
	"github.com/koopa0/pms-api-v2/internal/platform/database"
	"github.com/koopa0/pms-api-v2/internal/platform/email"
	"github.com/koopa0/pms-api-v2/internal/platform/logger"
	"github.com/koopa0/pms-api-v2/internal/platform/storage"
	"github.com/koopa0/pms-api-v2/internal/validator"
	"github.com/koopa0/pms-api-v2/sqlc"
)

// Server 代表 API 伺服器
// 統一管理所有依賴項、路由和伺服器生命週期
type Server struct {
	config              *config.Config
	db                  *database.DB
	queries             *sqlc.Queries
	logger              *logger.Logger
	router              *http.ServeMux
	httpServer          *http.Server
	tokenService        *auth.TokenService
	authMiddleware      *auth.Middleware
	authHandler         *auth.Handler
	userHandler         *user.Handler
	projectHandler      *project.Handler
	productHandler      *product.Handler
	quoteHandler        *quote.Handler
	registrationHandler *registration.Handler
}

// NewServer 建立新的 API 伺服器
// 初始化所有服務層、處理器和中間件
func NewServer(cfg *config.Config, db *database.DB, logger *logger.Logger) *Server {
	// 建立 sqlc 查詢實例
	queries := sqlc.New(db.Pool())

	// 建立 JWT Token 服務
	tokenService, err := auth.NewTokenService(&cfg.JWT)
	if err != nil {
		logger.Error("failed to initialize token service", "error", err)
		panic(err)
	}

	// 建立認證中間件
	authMiddleware := auth.NewMiddleware(tokenService)

	// 建立電子郵件服務
	emailService, err := email.New(&cfg.Email, logger.Logger)
	if err != nil {
		logger.Error("failed to initialize email service", "error", err)
		// Email service is optional, continue without it
		emailService = nil
	}

	// 建立儲存服務
	// TODO: Replace with actual MinioClient when configured
	var minioClient storage.MinioClient = nil
	fileService := storage.NewFileService(logger, minioClient, 10*1024*1024) // 10MB max file size
	excelService := storage.NewExcelService(logger)

	// 建立服務層
	authService := auth.NewService(queries, tokenService, logger)
	userService := user.NewService(queries, logger)
	projectService := project.NewService(db, logger.Logger)
	productService := product.NewService(db, logger.Logger)
	quoteService := quote.NewService(db, logger.Logger)
	registrationService := registration.New(db, emailService, logger.Logger)

	// 建立驗證器
	validator := validator.NewValidator()

	// 建立處理器層
	authHandler := auth.NewHandler(authService, logger, cfg.Env == "production")
	userHandler := user.NewHandler(userService, logger)
	projectHandler := project.NewHandler(projectService, validator, logger.Logger)
	productHandler := product.NewHandler(productService, fileService, excelService, logger)
	quoteHandler := quote.NewHandler(quoteService, logger)
	registrationHandler := registration.NewHandler(registrationService)

	s := &Server{
		config:              cfg,
		db:                  db,
		queries:             queries,
		logger:              logger,
		router:              http.NewServeMux(),
		tokenService:        tokenService,
		authMiddleware:      authMiddleware,
		authHandler:         authHandler,
		userHandler:         userHandler,
		projectHandler:      projectHandler,
		productHandler:      productHandler,
		quoteHandler:        quoteHandler,
		registrationHandler: registrationHandler,
	}

	// 設定路由
	s.setupRoutes()

	// 建立 HTTP 伺服器
	s.httpServer = &http.Server{
		Addr:           fmt.Sprintf(":%s", cfg.Server.Port),
		Handler:        s.router,
		ReadTimeout:    cfg.Server.ReadTimeout,
		WriteTimeout:   cfg.Server.WriteTimeout,
		IdleTimeout:    cfg.Server.IdleTimeout,
		MaxHeaderBytes: 1 << 20, // 1 MB 標頭大小限制
	}

	return s
}

// setupRoutes 設定所有 API 路由
// 使用 Go 1.22+ 的增強路由模式
func (s *Server) setupRoutes() {
	// 健康檢查端點
	s.router.HandleFunc("GET /health", s.handleHealth)

	// API 版本端點
	s.router.HandleFunc("GET /api/v1/version", s.handleVersion)

	// 認證相關端點
	s.router.HandleFunc("POST /api/v1/login", s.authHandler.Login)
	s.router.HandleFunc("POST /api/v1/logout", s.authMiddleware.Authenticate(s.authHandler.Logout))

	// 註冊相關端點
	s.router.HandleFunc("POST /api/v1/register", s.registrationHandler.Register)
	s.router.HandleFunc("GET /api/v1/registrations/pending", s.authMiddleware.Authenticate(s.registrationHandler.ListPending))
	s.router.HandleFunc("GET /api/v1/registrations/{id}", s.authMiddleware.Authenticate(s.registrationHandler.GetRegistration))
	s.router.HandleFunc("PUT /api/v1/registrations/{id}/approve", s.authMiddleware.Authenticate(s.registrationHandler.Approve))
	s.router.HandleFunc("PUT /api/v1/registrations/{id}/reject", s.authMiddleware.Authenticate(s.registrationHandler.Reject))

	// 用戶相關端點（需要認證）
	s.router.HandleFunc("GET /api/v1/users/profile", s.authMiddleware.Authenticate(s.userHandler.GetUserProfile))
	s.router.HandleFunc("PUT /api/v1/users/profile", s.authMiddleware.Authenticate(s.userHandler.UpdateUserProfile))
	s.router.HandleFunc("POST /api/v1/users/change-password", s.authMiddleware.Authenticate(s.userHandler.ChangePassword))

	// 管理員端點（僅限管理員）
	s.router.HandleFunc("GET /api/v1/users", s.authMiddleware.Authenticate(s.userHandler.ListUsers))
	s.router.HandleFunc("GET /api/v1/users/{id}", s.authMiddleware.Authenticate(s.userHandler.GetUserByID))
	s.router.HandleFunc("PATCH /api/v1/users/{id}/status", s.authMiddleware.Authenticate(s.userHandler.UpdateUserStatus))

	// 專案相關端點（需要認證）
	s.router.HandleFunc("GET /api/v1/projects", s.authMiddleware.Authenticate(s.projectHandler.ListProjects))
	s.router.HandleFunc("POST /api/v1/projects", s.authMiddleware.Authenticate(s.projectHandler.CreateProject))
	s.router.HandleFunc("GET /api/v1/projects/{id}", s.authMiddleware.Authenticate(s.projectHandler.GetProject))

	// 產品相關端點（需要認證）
	s.router.HandleFunc("GET /api/v1/products", s.authMiddleware.Authenticate(s.productHandler.ListProducts))
	s.router.HandleFunc("GET /api/v1/products/{id}", s.authMiddleware.Authenticate(s.productHandler.GetProduct))
	s.router.HandleFunc("PUT /api/v1/products/{id}", s.authMiddleware.Authenticate(s.productHandler.UpdateProduct))
	s.router.HandleFunc("POST /api/v1/products/import", s.authMiddleware.Authenticate(s.productHandler.ImportProducts))
	s.router.HandleFunc("POST /api/v1/products/import-json", s.authMiddleware.Authenticate(s.productHandler.ImportProductsJSON))
	s.router.HandleFunc("POST /api/v1/products/preview", s.authMiddleware.Authenticate(s.productHandler.PreviewImport))
	s.router.HandleFunc("POST /api/v1/products/upload", s.authMiddleware.Authenticate(s.productHandler.UploadImport))

	// 報價相關端點（需要認證）
	s.router.HandleFunc("GET /api/v1/quotes", s.authMiddleware.Authenticate(s.quoteHandler.ListQuotes))
	s.router.HandleFunc("POST /api/v1/quotes", s.authMiddleware.Authenticate(s.quoteHandler.CreateQuote))
	s.router.HandleFunc("GET /api/v1/quotes/{id}", s.authMiddleware.Authenticate(s.quoteHandler.GetQuote))
	s.router.HandleFunc("PUT /api/v1/quotes/{id}", s.authMiddleware.Authenticate(s.quoteHandler.UpdateQuote))
	s.router.HandleFunc("DELETE /api/v1/quotes/{id}", s.authMiddleware.Authenticate(s.quoteHandler.DeleteQuote))

	// 報價查詢端點
	s.router.HandleFunc("GET /api/v1/projects/{projectId}/quotes", s.authMiddleware.Authenticate(s.quoteHandler.GetProjectQuotes))
	s.router.HandleFunc("GET /api/v1/users/me/quotes", s.authMiddleware.Authenticate(s.quoteHandler.GetMyQuotes))

	// 報價審核端點（管理員功能）
	s.router.HandleFunc("GET /api/v1/quotes/pending", s.authMiddleware.Authenticate(s.quoteHandler.GetPendingQuotes))
	s.router.HandleFunc("PATCH /api/v1/quotes/{id}/review", s.authMiddleware.Authenticate(s.quoteHandler.ReviewQuote))
	s.router.HandleFunc("POST /api/v1/quotes/batch-review", s.authMiddleware.Authenticate(s.quoteHandler.BatchReviewQuotes))
}

// Start 啟動 API 伺服器
// 開始監聽設定的連接埠
func (s *Server) Start() error {
	s.logger.Info("starting API server",
		"port", s.config.Server.Port,
		"environment", s.config.Env)

	return s.httpServer.ListenAndServe()
}

// Shutdown 優雅地關閉伺服器
// 等待現有請求處理完成後再關閉
func (s *Server) Shutdown(ctx context.Context) error {
	s.logger.Info("shutting down API server")
	return s.httpServer.Shutdown(ctx)
}

// handleHealth 健康檢查處理器
// 檢查資料庫連線狀態，確保服務正常運作
func (s *Server) handleHealth(w http.ResponseWriter, r *http.Request) {
	// 檢查資料庫健康狀態
	if err := s.db.Health(r.Context()); err != nil {
		api.Error(w, http.StatusServiceUnavailable, api.ErrCodeInternalError, "database unhealthy")
		return
	}

	api.Success(w, map[string]string{
		"status":    "healthy",
		"service":   "pms-api-v2",
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// handleVersion 版本資訊處理器
// 返回 API 版本和環境資訊
func (s *Server) handleVersion(w http.ResponseWriter, r *http.Request) {
	api.Success(r.Context(), w, map[string]string{
		"version":     "v2.0.0",
		"service":     "pms-api",
		"environment": s.config.Env,
	})
}
