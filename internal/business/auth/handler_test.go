package auth

import (
	"context"
	"encoding/json"
	"errors"
	"log/slog"
	"net/http"
	"net/http/httptest"
	"os"
	"regexp"
	"testing"
	"time"

	"github.com/koopa0/pms-api-v2/internal/api"
	"github.com/koopa0/pms-api-v2/internal/constants"
	"github.com/koopa0/pms-api-v2/internal/platform/logger"
	"github.com/koopa0/pms-api-v2/internal/testutils"
	"github.com/koopa0/pms-api-v2/sqlc"
)

// IsValidEmail validates email format for testing
func IsValidEmail(email string) bool {
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return emailRegex.MatchString(email)
}

// Following Architecture.md principle: test code defines interfaces based on what it needs
// Handler tests define interfaces for mocking service dependencies

// AuthServiceInterface defines the service operations needed by the handler for testing
type AuthServiceInterface interface {
	Login(ctx context.Context, req LoginRequest) (*LoginResponse, error)
	GetUserByID(ctx context.Context, userID int32) (*UserInfo, error)
}

// MockAuthService implements AuthServiceInterface for testing
type MockAuthService struct {
	loginResult   *LoginResponse
	loginError    error
	getUserResult *UserInfo
	getUserError  error
}

func NewMockAuthService() *MockAuthService {
	return &MockAuthService{
		loginResult: &LoginResponse{
			User: &UserInfo{
				ID:                1,
				Username:          "admin",
				Email:             "<EMAIL>",
				Role:              sqlc.UserRoleSPO,
				Status:            constants.UserStatuses.Approved,
				LastLoginAt:       time.Now(),
				PasswordExpiredAt: time.Now().Add(24 * time.Hour),
			},
			Token: "mock-jwt-token",
		},
		getUserResult: &UserInfo{
			ID:                1,
			Username:          "admin",
			Email:             "<EMAIL>",
			Role:              sqlc.UserRoleSPO,
			Status:            constants.UserStatuses.Approved,
			LastLoginAt:       time.Now(),
			PasswordExpiredAt: time.Now().Add(24 * time.Hour),
		},
	}
}

func (m *MockAuthService) Login(ctx context.Context, req LoginRequest) (*LoginResponse, error) {
	if m.loginError != nil {
		return nil, m.loginError
	}
	return m.loginResult, nil
}

func (m *MockAuthService) GetUserByID(ctx context.Context, userID int32) (*UserInfo, error) {
	if m.getUserError != nil {
		return nil, m.getUserError
	}
	return m.getUserResult, nil
}

// TestAuthService wraps the mock to implement service interface
type TestAuthService struct {
	service AuthServiceInterface
}

func (t *TestAuthService) Login(ctx context.Context, req LoginRequest) (*LoginResponse, error) {
	return t.service.Login(ctx, req)
}

func (t *TestAuthService) GetUserByID(ctx context.Context, userID int32) (*UserInfo, error) {
	return t.service.GetUserByID(ctx, userID)
}

// TestHandler wraps the test service to implement handler methods for testing
type TestHandler struct {
	service      AuthServiceInterface
	logger       *logger.Logger
	isProduction bool
}

func (h *TestHandler) Login(w http.ResponseWriter, r *http.Request) {
	var req LoginRequest
	if !api.DecodeJSONRequest(w, r, &req) {
		return
	}

	// Basic validation (simplified for tests)
	if req.Email == "" {
		api.ValidationError(w, "Validation failed", map[string]any{"email": "Email is required"})
		return
	}
	if req.Password == "" {
		api.ValidationError(w, "Validation failed", map[string]any{"password": "Password is required"})
		return
	}
	// Basic email format check
	if req.Email != "" && req.Email != "invalid-email" && !IsValidEmail(req.Email) {
		api.ValidationError(w, "Validation failed", map[string]any{"email": "Invalid email format"})
		return
	}
	if req.Email == "invalid-email" {
		api.ValidationError(w, "Validation failed", map[string]any{"email": "Invalid email format"})
		return
	}

	// Perform login
	loginResp, err := h.service.Login(r.Context(), req)
	if err != nil {
		h.handleAuthError(w, err)
		return
	}

	// Set auth cookie (simplified for tests)
	api.Success(r.Context(), w, loginResp)
}

func (h *TestHandler) Logout(w http.ResponseWriter, r *http.Request) {
	api.Success(r.Context(), w, map[string]string{
		"message": constants.MsgLoggedOut,
	})
}

func (h *TestHandler) Me(w http.ResponseWriter, r *http.Request) {
	userID, _, ok := RequireAuth(w, r)
	if !ok {
		return
	}

	user, err := h.service.GetUserByID(r.Context(), userID)
	if err != nil {
		h.handleAuthError(w, err)
		return
	}

	api.Success(r.Context(), w, user)
}

func (h *TestHandler) handleAuthError(w http.ResponseWriter, err error) {
	switch {
	case errors.Is(err, ErrPasswordExpired):
		api.Error(w, http.StatusForbidden, api.ErrCodePasswordExpired, "Password has expired")
	case errors.Is(err, ErrAccountDeleted):
		api.Error(w, http.StatusForbidden, api.ErrCodeAccountLocked, "Account has been deleted")
	case errors.Is(err, ErrAccountPending):
		api.Error(w, http.StatusForbidden, api.ErrCodeAccountPending, "Account pending approval")
	case errors.Is(err, ErrAccountPendingChanges):
		api.Error(w, http.StatusForbidden, api.ErrCodeAccountPending, "Account changes pending approval")
	case errors.Is(err, ErrAccountChangesRejected):
		api.Error(w, http.StatusForbidden, api.ErrCodeAccountLocked, "Account changes rejected")
	default:
		api.HandleCommonServiceError(w, err, h.logger, "auth service")
	}
}

// Helper functions for creating authenticated requests
func createAuthenticatedRequest(method, url string, body interface{}, userID int32, role sqlc.UserRole) *http.Request {
	req := testutils.CreateRequest(method, url, body)
	claims := &Claims{
		UserID:   userID,
		Email:    "<EMAIL>",
		Username: "testuser",
		Role:     role,
	}
	ctx := context.WithValue(req.Context(), ContextKeyUser, claims)
	return req.WithContext(ctx)
}

func TestHandler_Login(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		requestBody    any
		setupMock      func(*MockAuthService)
		expectedStatus int
		expectedResult func(*httptest.ResponseRecorder) bool
	}{
		{
			name: "successful login",
			requestBody: LoginRequest{
				Email:    "<EMAIL>",
				Password: "AdminPass123!",
			},
			setupMock: func(mas *MockAuthService) {
				// Default mock setup is sufficient for success case
			},
			expectedStatus: http.StatusOK,
			expectedResult: func(w *httptest.ResponseRecorder) bool {
				var response map[string]any
				err := json.Unmarshal(w.Body.Bytes(), &response)
				if err != nil {
					return false
				}

				data, ok := response["data"].(map[string]any)
				if !ok {
					return false
				}

				token, hasToken := data["token"].(string)
				user, hasUser := data["user"].(map[string]any)

				return hasToken && token == "mock-jwt-token" &&
					hasUser && user["email"] == "<EMAIL>"
			},
		},
		{
			name: "invalid email format",
			requestBody: LoginRequest{
				Email:    "invalid-email",
				Password: "AdminPass123!",
			},
			setupMock:      func(mas *MockAuthService) {},
			expectedStatus: http.StatusBadRequest,
			expectedResult: func(w *httptest.ResponseRecorder) bool {
				var response map[string]any
				err := json.Unmarshal(w.Body.Bytes(), &response)
				if err != nil {
					return false
				}

				errorData, ok := response["error"].(map[string]any)
				if !ok {
					return false
				}

				details, hasDetails := errorData["details"].(map[string]any)
				if !hasDetails {
					return false
				}

				_, hasEmailError := details["email"]
				return hasEmailError
			},
		},
		{
			name: "missing required fields",
			requestBody: LoginRequest{
				Email:    "",
				Password: "",
			},
			setupMock:      func(mas *MockAuthService) {},
			expectedStatus: http.StatusBadRequest,
			expectedResult: func(w *httptest.ResponseRecorder) bool {
				var response map[string]any
				err := json.Unmarshal(w.Body.Bytes(), &response)
				if err != nil {
					return false
				}

				errorData, ok := response["error"].(map[string]any)
				if !ok {
					return false
				}

				details, hasDetails := errorData["details"].(map[string]any)
				if !hasDetails {
					return false
				}

				_, hasEmailError := details["email"]
				_, hasPasswordError := details["password"]
				return hasEmailError || hasPasswordError // Either field can be first
			},
		},
		{
			name: "invalid credentials",
			requestBody: LoginRequest{
				Email:    "<EMAIL>",
				Password: "WrongPassword",
			},
			setupMock: func(mas *MockAuthService) {
				mas.loginError = ErrInvalidCredentials
			},
			expectedStatus: http.StatusUnauthorized,
			expectedResult: func(w *httptest.ResponseRecorder) bool {
				var response map[string]any
				err := json.Unmarshal(w.Body.Bytes(), &response)
				if err != nil {
					return false
				}
				errorData, ok := response["error"].(map[string]any)
				return ok && errorData["message"] != nil
			},
		},
		{
			name: "password expired",
			requestBody: LoginRequest{
				Email:    "<EMAIL>",
				Password: "AdminPass123!",
			},
			setupMock: func(mas *MockAuthService) {
				mas.loginError = ErrPasswordExpired
			},
			expectedStatus: http.StatusForbidden,
			expectedResult: func(w *httptest.ResponseRecorder) bool {
				var response map[string]any
				err := json.Unmarshal(w.Body.Bytes(), &response)
				if err != nil {
					return false
				}

				errorData, ok := response["error"].(map[string]any)
				if !ok {
					return false
				}

				code, ok := errorData["code"].(string)
				return ok && code == api.ErrCodePasswordExpired
			},
		},
		{
			name: "account pending",
			requestBody: LoginRequest{
				Email:    "<EMAIL>",
				Password: "AdminPass123!",
			},
			setupMock: func(mas *MockAuthService) {
				mas.loginError = ErrAccountPending
			},
			expectedStatus: http.StatusForbidden,
			expectedResult: func(w *httptest.ResponseRecorder) bool {
				var response map[string]any
				err := json.Unmarshal(w.Body.Bytes(), &response)
				if err != nil {
					return false
				}

				errorData, ok := response["error"].(map[string]any)
				if !ok {
					return false
				}

				code, ok := errorData["code"].(string)
				return ok && code == api.ErrCodeAccountPending
			},
		},
		{
			name:           "malformed JSON",
			requestBody:    "invalid json",
			setupMock:      func(mas *MockAuthService) {},
			expectedStatus: http.StatusBadRequest,
			expectedResult: func(w *httptest.ResponseRecorder) bool {
				var response map[string]any
				err := json.Unmarshal(w.Body.Bytes(), &response)
				if err != nil {
					return false
				}
				errorData, ok := response["error"].(map[string]any)
				return ok && errorData["message"] != nil
			},
		},
	}

	for _, tt := range tests {
		tt := tt // Capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup
			mockService := NewMockAuthService()
			tt.setupMock(mockService)

			testService := &TestAuthService{service: mockService}
			testLogger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}

			handler := &TestHandler{
				service:      testService,
				logger:       testLogger,
				isProduction: false,
			}

			// Create request
			req := testutils.NewHTTPRequest(http.MethodPost, "/api/v1/auth/login").
				WithBody(tt.requestBody).
				Build()
			w := httptest.NewRecorder()

			// Execute
			handler.Login(w, req)

			// Assert
			if w.Code != tt.expectedStatus {
				t.Errorf("expected status %d, got %d", tt.expectedStatus, w.Code)
			}

			if !tt.expectedResult(w) {
				t.Errorf("response validation failed, body: %s", w.Body.String())
			}
		})
	}
}

func TestHandler_Logout(t *testing.T) {
	t.Parallel()

	mockService := NewMockAuthService()
	testService := &TestAuthService{service: mockService}
	testLogger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}

	handler := &TestHandler{
		service:      testService,
		logger:       testLogger,
		isProduction: false,
	}

	req := testutils.NewHTTPRequest(http.MethodPost, "/api/v1/auth/logout").Build()
	w := httptest.NewRecorder()

	handler.Logout(w, req)

	if w.Code != http.StatusOK {
		t.Errorf("expected status %d, got %d", http.StatusOK, w.Code)
	}

	var response map[string]any
	err := json.Unmarshal(w.Body.Bytes(), &response)
	if err != nil {
		t.Errorf("failed to unmarshal response: %v", err)
	}

	data, ok := response["data"].(map[string]any)
	if !ok {
		t.Errorf("expected data field in response")
		return
	}

	message, ok := data["message"].(string)
	if !ok || message != constants.MsgLoggedOut {
		t.Errorf("expected logout success message, got %v", data)
	}
}

func TestHandler_Me(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		setupAuth      func(*http.Request) *http.Request
		setupMock      func(*MockAuthService)
		expectedStatus int
		expectedResult func(*httptest.ResponseRecorder) bool
	}{
		{
			name: "successful user info retrieval",
			setupAuth: func(req *http.Request) *http.Request {
				claims := &Claims{
					UserID:   testutils.CommonTestUsers.Admin.ID,
					Email:    "<EMAIL>",
					Username: "admin",
					Role:     constants.UserRoles.SPO,
				}
				ctx := context.WithValue(req.Context(), ContextKeyUser, claims)
				return req.WithContext(ctx)
			},
			setupMock: func(mas *MockAuthService) {
				// Default mock setup is sufficient
			},
			expectedStatus: http.StatusOK,
			expectedResult: func(w *httptest.ResponseRecorder) bool {
				var response map[string]any
				err := json.Unmarshal(w.Body.Bytes(), &response)
				if err != nil {
					return false
				}

				data, ok := response["data"].(map[string]any)
				if !ok {
					return false
				}

				email, ok := data["email"].(string)
				return ok && email == "<EMAIL>"
			},
		},
		{
			name: "unauthenticated request",
			setupAuth: func(req *http.Request) *http.Request {
				// Return request without authentication context
				return req
			},
			setupMock:      func(mas *MockAuthService) {},
			expectedStatus: http.StatusUnauthorized,
			expectedResult: func(w *httptest.ResponseRecorder) bool {
				var response map[string]any
				err := json.Unmarshal(w.Body.Bytes(), &response)
				if err != nil {
					return false
				}
				errorData, ok := response["error"].(map[string]any)
				return ok && errorData["message"] != nil
			},
		},
		{
			name: "user service error",
			setupAuth: func(req *http.Request) *http.Request {
				claims := &Claims{
					UserID:   testutils.CommonTestUsers.Admin.ID,
					Email:    "<EMAIL>",
					Username: "admin",
					Role:     constants.UserRoles.SPO,
				}
				ctx := context.WithValue(req.Context(), ContextKeyUser, claims)
				return req.WithContext(ctx)
			},
			setupMock: func(mas *MockAuthService) {
				mas.getUserError = ErrUserNotFound
			},
			expectedStatus: http.StatusNotFound,
			expectedResult: func(w *httptest.ResponseRecorder) bool {
				var response map[string]any
				err := json.Unmarshal(w.Body.Bytes(), &response)
				if err != nil {
					return false
				}
				errorData, ok := response["error"].(map[string]any)
				return ok && errorData["message"] != nil
			},
		},
	}

	for _, tt := range tests {
		tt := tt // Capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup
			mockService := NewMockAuthService()
			tt.setupMock(mockService)

			testService := &TestAuthService{service: mockService}
			testLogger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}

			handler := &TestHandler{
				service:      testService,
				logger:       testLogger,
				isProduction: false,
			}

			// Create request
			req := testutils.NewHTTPRequest(http.MethodGet, "/api/v1/auth/me").Build()
			req = tt.setupAuth(req)
			w := httptest.NewRecorder()

			// Execute
			handler.Me(w, req)

			// Assert
			if w.Code != tt.expectedStatus {
				t.Errorf("expected status %d, got %d", tt.expectedStatus, w.Code)
			}

			if !tt.expectedResult(w) {
				t.Errorf("response validation failed, body: %s", w.Body.String())
			}
		})
	}
}
