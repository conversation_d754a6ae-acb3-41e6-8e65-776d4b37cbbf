// Package quote 提供報價管理相關的 HTTP 處理器
// 負責處理報價相關的 REST API 端點，包含 CRUD 操作和審核功能
package quote

import (
	"net/http"
	"strconv"

	"github.com/koopa0/pms-api-v2/internal/api"
	"github.com/koopa0/pms-api-v2/internal/business/auth"
	"github.com/koopa0/pms-api-v2/internal/constants"
	"github.com/koopa0/pms-api-v2/internal/platform/logger"
	"github.com/koopa0/pms-api-v2/internal/validator"
	"github.com/koopa0/pms-api-v2/sqlc"
)

// Handler 報價 HTTP 處理器
type Handler struct {
	service *Service
	logger  *logger.Logger
}

// NewHandler 創建新的報價處理器實例
func NewHandler(service *Service, logger *logger.Logger) *Handler {
	return &Handler{
		service: service,
		logger:  logger,
	}
}

// CreateQuote 處理 POST /api/v1/quotes
// 創建新報價
func (h *Handler) CreateQuote(w http.ResponseWriter, r *http.Request) {
	// 驗證用戶登入
	userID, role, ok := auth.RequireAuth(w, r)
	if !ok {
		return
	}

	// 解析請求
	var req CreateQuoteRequest
	if !api.DecodeJSONRequest(w, r, &req) {
		return
	}

	// 驗證輸入
	if err := h.validateCreateQuoteRequest(&req, sqlc.UserRole(role)); err != nil {
		api.BadRequest(w, err.Error())
		return
	}

	// 創建報價
	quote, err := h.service.CreateQuote(r.Context(), req, userID)
	if err != nil {
		h.handleServiceError(w, err)
		return
	}

	api.Success(r.Context(), w, quote)
}

// GetQuote 處理 GET /api/v1/quotes/{id}
// 獲取報價詳情
func (h *Handler) GetQuote(w http.ResponseWriter, r *http.Request) {
	// 驗證用戶登入
	userID, role, ok := auth.RequireAuth(w, r)
	if !ok {
		return
	}

	// 獲取報價 ID
	quoteID, ok := api.ParseIDParam(w, r, "id")
	if !ok {
		return
	}

	// 獲取報價詳情
	quote, err := h.service.GetQuoteWithDetails(r.Context(), quoteID)
	if err != nil {
		h.handleServiceError(w, err)
		return
	}

	// 檢查權限 - 只有擁有者或管理員可以查看
	if quote.UserID != userID && role != string(constants.UserRoles.SPO) && role != string(constants.UserRoles.CISA) {
		api.Forbidden(w, constants.ErrMsgForbidden)
		return
	}

	api.Success(r.Context(), w, quote)
}

// UpdateQuote 處理 PUT /api/v1/quotes/{id}
// 更新報價
func (h *Handler) UpdateQuote(w http.ResponseWriter, r *http.Request) {
	// 驗證用戶登入
	userID, _, ok := auth.RequireAuth(w, r)
	if !ok {
		return
	}

	// 獲取報價 ID
	quoteID, ok := api.ParseIDParam(w, r, "id")
	if !ok {
		return
	}

	// 解析請求
	var req UpdateQuoteRequest
	if !api.DecodeJSONRequest(w, r, &req) {
		return
	}

	// 驗證輸入
	if err := h.validateUpdateQuoteRequest(&req); err != nil {
		api.BadRequest(w, err.Error())
		return
	}

	// 更新報價
	quote, err := h.service.UpdateQuote(r.Context(), quoteID, req, userID)
	if err != nil {
		h.handleServiceError(w, err)
		return
	}

	api.Success(r.Context(), w, quote)
}

// DeleteQuote 處理 DELETE /api/v1/quotes/{id}
// 刪除報價
func (h *Handler) DeleteQuote(w http.ResponseWriter, r *http.Request) {
	// 驗證用戶登入
	userID, _, ok := auth.RequireAuth(w, r)
	if !ok {
		return
	}

	// 獲取報價 ID
	quoteID, ok := api.ParseIDParam(w, r, "id")
	if !ok {
		return
	}

	// 刪除報價
	err := h.service.DeleteQuote(r.Context(), quoteID, userID)
	if err != nil {
		h.handleServiceError(w, err)
		return
	}

	api.Success(r.Context(), w, map[string]string{"message": "報價已刪除"})
}

// ListQuotes 處理 GET /api/v1/quotes
// 列出報價
func (h *Handler) ListQuotes(w http.ResponseWriter, r *http.Request) {
	// 驗證用戶登入
	userID, role, ok := auth.RequireAuth(w, r)
	if !ok {
		return
	}

	// 解析查詢參數
	req := h.parseListQuotesRequest(r)

	// 根據角色限制查詢範圍
	if role == string(constants.UserRoles.Company) {
		// 廠商只能看自己的報價
		req.UserID = &userID
	}

	// 查詢報價列表
	response, err := h.service.ListQuotes(r.Context(), req)
	if err != nil {
		h.handleServiceError(w, err)
		return
	}

	api.Success(r.Context(), w, response)
}

// GetProjectQuotes 處理 GET /api/v1/projects/{projectId}/quotes
// 獲取專案的所有報價
func (h *Handler) GetProjectQuotes(w http.ResponseWriter, r *http.Request) {
	// 驗證用戶登入（管理員權限）
	if _, _, ok := auth.RequireRole(w, r, string(constants.UserRoles.SPO), string(constants.UserRoles.CISA)); !ok {
		return
	}

	// 獲取專案 ID
	projectID, ok := api.ParseIDParam(w, r, "projectId")
	if !ok {
		return
	}

	// 獲取專案報價
	quotes, err := h.service.GetQuotesByProject(r.Context(), projectID)
	if err != nil {
		h.handleServiceError(w, err)
		return
	}

	api.Success(r.Context(), w, quotes)
}

// GetMyQuotes 處理 GET /api/v1/users/me/quotes
// 獲取當前用戶的報價
func (h *Handler) GetMyQuotes(w http.ResponseWriter, r *http.Request) {
	// 驗證用戶登入
	userID, _, ok := auth.RequireAuth(w, r)
	if !ok {
		return
	}

	// 解析分頁參數
	pagination := api.ParseRequestPagination(r)

	// 獲取用戶報價
	response, err := h.service.GetQuotesByUser(r.Context(), userID, pagination)
	if err != nil {
		h.handleServiceError(w, err)
		return
	}

	api.Success(r.Context(), w, response)
}

// GetPendingQuotes 處理 GET /api/v1/quotes/pending
// 獲取待審核報價（管理員功能）
func (h *Handler) GetPendingQuotes(w http.ResponseWriter, r *http.Request) {
	// 驗證管理員權限
	if _, _, ok := auth.RequireRole(w, r, string(constants.UserRoles.SPO), string(constants.UserRoles.CISA)); !ok {
		return
	}

	// 獲取待審核報價
	quotes, err := h.service.GetPendingQuotes(r.Context())
	if err != nil {
		h.handleServiceError(w, err)
		return
	}

	api.Success(r.Context(), w, quotes)
}

// ReviewQuote 處理 PATCH /api/v1/quotes/{id}/review
// 審核報價（管理員功能）
func (h *Handler) ReviewQuote(w http.ResponseWriter, r *http.Request) {
	// 驗證管理員權限
	reviewerID, _, ok := auth.RequireRole(w, r, string(constants.UserRoles.SPO), string(constants.UserRoles.CISA))
	if !ok {
		return
	}

	// 獲取報價 ID
	quoteID, ok := api.ParseIDParam(w, r, "id")
	if !ok {
		return
	}

	// 解析請求
	var req ReviewQuoteRequest
	if !api.DecodeJSONRequest(w, r, &req) {
		return
	}

	// 驗證輸入
	if err := h.validateReviewQuoteRequest(&req); err != nil {
		api.BadRequest(w, err.Error())
		return
	}

	// 審核報價
	quote, err := h.service.ReviewQuote(r.Context(), quoteID, req, reviewerID)
	if err != nil {
		h.handleServiceError(w, err)
		return
	}

	api.Success(r.Context(), w, quote)
}

// BatchReviewQuotes 處理 POST /api/v1/quotes/batch-review
// 批量審核報價（管理員功能）
func (h *Handler) BatchReviewQuotes(w http.ResponseWriter, r *http.Request) {
	// 驗證管理員權限
	reviewerID, _, ok := auth.RequireRole(w, r, string(constants.UserRoles.SPO), string(constants.UserRoles.CISA))
	if !ok {
		return
	}

	// 解析請求
	var req struct {
		QuoteIDs    []int32          `json:"quote_ids"`
		Status      sqlc.QuoteStatus `json:"status"`
		AdminRemark string           `json:"admin_remark"`
	}
	if !api.DecodeJSONRequest(w, r, &req) {
		return
	}

	// 驗證輸入
	if len(req.QuoteIDs) == 0 {
		api.BadRequest(w, "請選擇要審核的報價")
		return
	}
	if req.Status != constants.QuoteStatuses.Approved && req.Status != constants.QuoteStatuses.Rejected {
		api.BadRequest(w, constants.ErrMsgInvalidStatus)
		return
	}

	// 批量審核
	err := h.service.BatchReviewQuotes(r.Context(), req.QuoteIDs, req.Status, req.AdminRemark, reviewerID)
	if err != nil {
		h.handleServiceError(w, err)
		return
	}

	api.Success(r.Context(), w, map[string]interface{}{
		"message": "批量審核完成",
		"count":   len(req.QuoteIDs),
		"status":  req.Status,
	})
}

// 驗證方法

// validateCreateQuoteRequest 驗證創建報價請求
func (h *Handler) validateCreateQuoteRequest(req *CreateQuoteRequest, userRole sqlc.UserRole) error {
	v := validator.NewValidator()

	// 基本欄位驗證
	if req.ProjectID == 0 {
		v.AddError("project_id", "專案ID不能為空")
	}
	if req.ProductID == 0 {
		v.AddError("product_id", "產品ID不能為空")
	}
	v.Required("quote_type", string(req.QuoteType))

	// 根據角色驗證報價類型
	switch userRole {
	case constants.UserRoles.Company:
		if req.QuoteType != constants.QuoteTypes.Vendor {
			v.AddError("quote_type", "廠商只能提交廠商報價")
		}
	case constants.UserRoles.CISA:
		if req.QuoteType != constants.QuoteTypes.CISA {
			v.AddError("quote_type", "軟協只能提交軟協報價")
		}
	case constants.UserRoles.SPO:
		if req.QuoteType != constants.QuoteTypes.Office {
			v.AddError("quote_type", "辦公室只能提交辦公室報價")
		}
	}

	// 至少要有一個價格
	if req.MarketPrice == nil && req.InternetPrice == nil && req.OriginalPrice == nil &&
		req.PromotionPrice == nil && req.BidPrice == nil {
		v.AddError("price", "請至少填寫一個價格")
	}

	// 價格合理性驗證
	if req.MarketPrice != nil && *req.MarketPrice < 0 {
		v.AddError("market_price", "市售價不能為負數")
	}
	if req.InternetPrice != nil && *req.InternetPrice < 0 {
		v.AddError("internet_price", "網路價不能為負數")
	}
	if req.OriginalPrice != nil && *req.OriginalPrice < 0 {
		v.AddError("original_price", "原廠價不能為負數")
	}
	if req.PromotionPrice != nil && *req.PromotionPrice < 0 {
		v.AddError("promotion_price", "促銷價不能為負數")
	}
	if req.BidPrice != nil && *req.BidPrice < 0 {
		v.AddError("bid_price", "決標價不能為負數")
	}

	// 備註長度限制
	v.MaxLength("remark", req.Remark, 500)

	if errs := v.Errors(); errs != nil {
		return errs
	}
	return nil
}

// validateUpdateQuoteRequest 驗證更新報價請求
func (h *Handler) validateUpdateQuoteRequest(req *UpdateQuoteRequest) error {
	v := validator.NewValidator()

	// 至少要有一個價格
	if req.MarketPrice == nil && req.InternetPrice == nil && req.OriginalPrice == nil &&
		req.PromotionPrice == nil && req.BidPrice == nil {
		v.AddError("price", "請至少填寫一個價格")
	}

	// 價格合理性驗證
	if req.MarketPrice != nil && *req.MarketPrice < 0 {
		v.AddError("market_price", "市售價不能為負數")
	}
	if req.InternetPrice != nil && *req.InternetPrice < 0 {
		v.AddError("internet_price", "網路價不能為負數")
	}
	if req.OriginalPrice != nil && *req.OriginalPrice < 0 {
		v.AddError("original_price", "原廠價不能為負數")
	}
	if req.PromotionPrice != nil && *req.PromotionPrice < 0 {
		v.AddError("promotion_price", "促銷價不能為負數")
	}
	if req.BidPrice != nil && *req.BidPrice < 0 {
		v.AddError("bid_price", "決標價不能為負數")
	}

	// 備註長度限制
	v.MaxLength("remark", req.Remark, 500)

	if errs := v.Errors(); errs != nil {
		return errs
	}
	return nil
}

// validateReviewQuoteRequest 驗證審核報價請求
func (h *Handler) validateReviewQuoteRequest(req *ReviewQuoteRequest) error {
	v := validator.NewValidator()

	v.Required("status", string(req.Status))
	v.OneOf("status", string(req.Status), []string{
		string(constants.QuoteStatuses.Approved),
		string(constants.QuoteStatuses.Rejected),
	})

	// 退件必須填寫原因
	if req.Status == constants.QuoteStatuses.Rejected && req.AdminRemark == "" {
		v.AddError("admin_remark", "退件必須填寫原因")
	}

	v.MaxLength("admin_remark", req.AdminRemark, 500)

	if errs := v.Errors(); errs != nil {
		return errs
	}
	return nil
}

// 輔助方法

// parseListQuotesRequest 解析列出報價請求參數
func (h *Handler) parseListQuotesRequest(r *http.Request) ListQuotesRequest {
	req := ListQuotesRequest{
		Pagination: api.ParseRequestPagination(r),
	}

	// 解析篩選參數
	if projectID := r.URL.Query().Get("project_id"); projectID != "" {
		if id, err := strconv.ParseInt(projectID, 10, 32); err == nil {
			projectID32 := int32(id)
			req.ProjectID = &projectID32
		}
	}

	if productID := r.URL.Query().Get("product_id"); productID != "" {
		if id, err := strconv.ParseInt(productID, 10, 32); err == nil {
			productID32 := int32(id)
			req.ProductID = &productID32
		}
	}

	if userID := r.URL.Query().Get("user_id"); userID != "" {
		if id, err := strconv.ParseInt(userID, 10, 32); err == nil {
			userID32 := int32(id)
			req.UserID = &userID32
		}
	}

	if quoteType := r.URL.Query().Get("quote_type"); quoteType != "" {
		qt := sqlc.QuoteType(quoteType)
		req.QuoteType = &qt
	}

	if status := r.URL.Query().Get("status"); status != "" {
		st := sqlc.QuoteStatus(status)
		req.Status = &st
	}

	if batchID := r.URL.Query().Get("batch_id"); batchID != "" {
		if id, err := strconv.ParseInt(batchID, 10, 32); err == nil {
			batchID32 := int32(id)
			req.BatchID = &batchID32
		}
	}

	return req
}

// handleServiceError 處理服務層錯誤
func (h *Handler) handleServiceError(w http.ResponseWriter, err error) {
	api.HandleCommonServiceError(w, err, h.logger, "quote service")
}
