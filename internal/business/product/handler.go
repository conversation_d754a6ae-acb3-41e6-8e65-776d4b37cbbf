package product

import (
	"context"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"strconv"
	"time"

	"github.com/koopa0/pms-api-v2/internal/api"
	"github.com/koopa0/pms-api-v2/internal/business/auth"
	"github.com/koopa0/pms-api-v2/internal/constants"
	"github.com/koopa0/pms-api-v2/internal/platform/logger"
	"github.com/koopa0/pms-api-v2/internal/platform/storage"
)

// <PERSON><PERSON> handles HTTP requests for product management
type Handler struct {
	productService *Service
	fileService    *storage.FileService
	excelService   *storage.ExcelService
	validator      *ExcelValidator
	logger         *logger.Logger
}

// NewHandler creates a new product handler
func NewHandler(productService *Service, fileService *storage.FileService, excelService *storage.ExcelService, logger *logger.Logger) *Handler {
	return &Handler{
		productService: productService,
		fileService:    fileService,
		excelService:   excelService,
		validator:      NewProductExcelValidator(),
		logger:         logger,
	}
}

// ImportProducts handles POST /api/v1/products/import
func (h *Handler) ImportProducts(w http.ResponseWriter, r *http.Request) {
	// Check content type
	contentType := r.Header.Get("Content-Type")
	if contentType != "text/csv" && contentType != "application/csv" {
		api.BadRequest(w, "content type must be text/csv or application/csv")
		return
	}

	// Read CSV data
	csvData, err := io.ReadAll(r.Body)
	if err != nil {
		api.BadRequest(w, "failed to read CSV data")
		return
	}

	if len(csvData) == 0 {
		api.BadRequest(w, "CSV data is empty")
		return
	}

	// Import products
	result, err := h.productService.ImportProductsFromCSV(r.Context(), csvData)
	if err != nil {
		h.handleServiceErrorWithContext(r.Context(), w, err)
		return
	}

	api.Success(r.w, result)
}

// GetProduct handles GET /api/v1/products/{id}
func (h *Handler) GetProduct(w http.ResponseWriter, r *http.Request) {
	productID, ok := api.ParseIDParam(w, r, "id")
	if !ok {
		return
	}

	product, err := h.productService.GetProductByID(r.Context(), productID)
	if err != nil {
		h.handleServiceErrorWithContext(r.Context(), w, err)
		return
	}

	api.Success(w, product)
}

// UpdateProduct handles PUT /api/v1/products/{id}
func (h *Handler) UpdateProduct(w http.ResponseWriter, r *http.Request) {
	// 驗證用戶登入
	userID, role, ok := auth.RequireAuth(w, r)
	if !ok {
		return
	}

	// 只有管理員可以更新產品
	if role != string(constants.UserRoles.SPO) && role != string(constants.UserRoles.CISA) {
		api.Forbidden(w, "無權限執行此操作")
		return
	}

	// 獲取產品 ID
	productID, ok := api.ParseIDParam(w, r, "id")
	if !ok {
		return
	}

	// 解析請求
	var req UpdateProductRequest
	if !api.DecodeJSONRequest(w, r, &req) {
		return
	}

	// 更新產品
	product, err := h.productService.UpdateProduct(r.Context(), productID, req, userID)
	if err != nil {
		h.handleServiceErrorWithContext(r.Context(), w, err)
		return
	}

	api.Success(w, product)
}

// ListProducts handles GET /api/v1/products
func (h *Handler) ListProducts(w http.ResponseWriter, r *http.Request) {
	pagination := api.ParseRequestPagination(r)
	req := ListProductsRequest{
		CompanyVat: r.URL.Query().Get("company_vat"),
		Category:   r.URL.Query().Get("category"),
		Pagination: pagination,
	}

	// Parse is_active parameter
	if isActiveStr := r.URL.Query().Get("is_active"); isActiveStr != "" {
		if isActive, err := strconv.ParseBool(isActiveStr); err == nil {
			req.IsActive = &isActive
		}
	}

	products, err := h.productService.ListProducts(r.Context(), req)
	if err != nil {
		h.handleServiceErrorWithContext(r.Context(), w, err)
		return
	}

	api.Success(w, products)
}

// ImportProductsJSONRequest represents JSON-based product import request
type ImportProductsJSONRequest struct {
	ProjectID int32                    `json:"project_id"`
	Products  []map[string]interface{} `json:"products"`
}

// ImportProductsJSON handles JSON-based product import
// POST /api/v1/products/import-json
func (h *Handler) ImportProductsJSON(w http.ResponseWriter, r *http.Request) {
	// Check authentication and get user info
	userID, _, ok := auth.RequireAuth(w, r)
	if !ok {
		return
	}

	var req ImportProductsJSONRequest
	if !api.DecodeJSONRequest(w, r, &req) {
		return
	}

	// Validate request
	if req.ProjectID <= 0 {
		api.BadRequest(w, "project_id must be a positive integer")
		return
	}

	if len(req.Products) == 0 {
		api.BadRequest(w, "products array cannot be empty")
		return
	}

	// Check import permission
	err := h.validateImportPermission(r.Context(), userID, req.ProjectID)
	if err != nil {
		api.Forbidden(w, "無權限執行此操作")
		return
	}

	// Convert to service request format
	serviceReq := ImportProductsRequest{
		ProjectID: req.ProjectID,
		UserID:    userID,
		Data:      req.Products,
	}

	// Import products using the structured data method
	result, err := h.productService.ImportProductsFromData(r.Context(), serviceReq)
	if err != nil {
		h.handleServiceErrorWithContext(r.Context(), w, err)
		return
	}

	api.Success(w, result)
}

// PreviewImport handles POST /api/v1/products/preview
// 驗證 Excel 檔案格式，返回驗證結果但不實際匯入
func (h *Handler) PreviewImport(w http.ResponseWriter, r *http.Request) {
	// 檢查認證
	userID, _, ok := auth.RequireAuth(w, r)
	if !ok {
		api.Unauthorized(w, "請先登入")
		return
	}

	// 解析專案ID
	projectIDStr := r.URL.Query().Get("project_id")
	if projectIDStr == "" {
		api.BadRequest(w, "缺少 project_id 參數")
		return
	}

	projectID, err := strconv.ParseInt(projectIDStr, 10, 32)
	if err != nil {
		api.BadRequest(w, "project_id 格式錯誤")
		return
	}

	// 檢查權限
	if err = h.validateImportPermission(r.Context(), userID, int32(projectID)); err != nil {
		api.Forbidden(w, "無權限執行此操作")
		return
	}

	// 檢查文件
	file, header, err := r.FormFile("file")
	if err != nil {
		api.BadRequest(w, "請選擇要上傳的檔案")
		return
	}
	defer func(file multipart.File) {
		if err = file.Close(); err != nil {
			h.logger.Error("Failed to close file", "error", err)
		}
	}(file)

	// 驗證檔案
	validateReq := storage.ValidateFileRequest{
		Filename:          header.Filename,
		ContentType:       header.Header.Get("Content-Type"),
		Size:              header.Size,
		AllowedExtensions: storage.GetSupportedExcelExtensions(),
		MaxSizeMB:         30, // 30MB limit
	}

	if err = h.fileService.ValidateFile(validateReq); err != nil {
		api.BadRequest(w, err.Error())
		return
	}

	// 使用 Excel 服務預覽檔案
	// 設定預覽行數限制，0 表示預覽所有資料
	maxRows := 0
	if maxRowsStr := r.URL.Query().Get("max_rows"); maxRowsStr != "" {
		if maxRowsVal, err := strconv.Atoi(maxRowsStr); err == nil && maxRowsVal > 0 {
			maxRows = maxRowsVal
		}
	}

	result, err := h.excelService.PreviewExcelFile(r.Context(), file, h.validator, maxRows)
	if err != nil {
		h.handleServiceErrorWithContext(r.Context(), w, err)
		return
	}

	// 返回預覽結果
	api.Success(w, map[string]interface{}{
		"valid":        len(result.Errors) == 0,
		"total_rows":   result.TotalRows,
		"valid_rows":   result.ValidRows,
		"invalid_rows": result.InvalidRows,
		"errors":       result.Errors,
		"project_id":   projectID,
		"filename":     header.Filename,
		"processed_at": time.Now().Format(time.RFC3339),
	})
}

// UploadImport handles POST /api/v1/products/upload
// 實際執行 Excel 檔案匯入，處理重複資料並記錄日誌
func (h *Handler) UploadImport(w http.ResponseWriter, r *http.Request) {
	// 檢查認證
	userID, _, ok := auth.RequireAuth(w, r)
	if !ok {
		return
	}

	// 解析專案ID
	projectIDStr := r.URL.Query().Get("project_id")
	if projectIDStr == "" {
		api.BadRequest(w, "缺少 project_id 參數")
		return
	}

	projectID, err := strconv.ParseInt(projectIDStr, 10, 32)
	if err != nil {
		api.BadRequest(w, "project_id 格式錯誤")
		return
	}

	// 檢查權限
	err = h.validateImportPermission(r.Context(), userID, int32(projectID))
	if err != nil {
		api.Forbidden(w, "無權限執行此操作")
		return
	}

	// 檢查文件
	file, header, err := r.FormFile("file")
	if err != nil {
		api.BadRequest(w, "請選擇要上傳的檔案")
		return
	}
	defer func(file multipart.File) {
		if err = file.Close(); err != nil {
			h.logger.Error("Failed to close file", "error", err)
		}
	}(file)

	// 驗證檔案
	validateReq := storage.ValidateFileRequest{
		Filename:          header.Filename,
		ContentType:       header.Header.Get("Content-Type"),
		Size:              header.Size,
		AllowedExtensions: storage.GetSupportedExcelExtensions(),
		MaxSizeMB:         30, // 30MB limit
	}

	if err = h.fileService.ValidateFile(validateReq); err != nil {
		api.BadRequest(w, err.Error())
		return
	}

	// 處理 Excel 檔案並取得資料
	excelResult, err := h.excelService.ProcessExcelFile(r.Context(), file, h.validator)
	if err != nil {
		h.handleServiceErrorWithContext(r.Context(), w, err)
		return
	}

	// 如果有驗證錯誤，返回錯誤
	if len(excelResult.Errors) > 0 {
		api.Success(w, map[string]interface{}{
			"success":       false,
			"total_rows":    excelResult.TotalRows,
			"imported_rows": 0,
			"failed_rows":   excelResult.InvalidRows,
			"errors":        excelResult.Errors,
			"message":       "檔案包含驗證錯誤，請修正後重新上傳",
		})
		return
	}

	// 使用 product service 匯入資料
	importReq := ImportProductsRequest{
		ProjectID: int32(projectID),
		UserID:    userID,
		Data:      excelResult.Data,
	}

	importResult, err := h.productService.ImportProductsFromData(r.Context(), importReq)
	if err != nil {
		h.handleServiceErrorWithContext(r.Context(), w, err)
		return
	}

	// 返回匯入結果
	api.Success(w, map[string]interface{}{
		"success":       importResult.SuccessCount > 0,
		"total_rows":    excelResult.TotalRows,
		"imported_rows": importResult.SuccessCount,
		"failed_rows":   importResult.FailureCount,
		"errors":        importResult.Errors,
		"message":       h.getImportMessage(importResult),
		"processed_at":  time.Now().Format(time.RFC3339),
	})
}

// GetImportTemplate handles GET /api/v1/products/template
// 返回 Excel 匯入模板
func (h *Handler) GetImportTemplate(w http.ResponseWriter, r *http.Request) {
	// 檢查認證
	if _, _, ok := auth.RequireAuth(w, r); !ok {
		h.logger.Error("Failed to get import template", "error", "Unauthorized")
		return
	}

	// 生成 Excel 模板
	template, err := h.excelService.GenerateTemplate(h.validator)
	if err != nil {
		h.handleServiceErrorWithContext(r.Context(), w, err)
		return
	}

	// 設定回應標頭為 Excel 格式
	w.Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	w.Header().Set("Content-Disposition", "attachment; filename=\"product_import_template.xlsx\"")

	// 返回模板
	if _, err = w.Write(template); err != nil {
		return
	}
}

// Helper methods

// validateImportPermission 檢查用戶是否有權限匯入產品
func (h *Handler) validateImportPermission(ctx context.Context, userID int32, projectID int32) error {
	// Get user claims from context
	claims, ok := auth.GetUserFromContext(ctx)
	if !ok || claims == nil {
		return fmt.Errorf("未授權")
	}

	// SPO and CISA can import products for any project
	if claims.Role == constants.UserRoles.SPO || claims.Role == constants.UserRoles.CISA {
		return nil
	}

	// Company users cannot import products
	if claims.Role == constants.UserRoles.Company {
		return fmt.Errorf("廠商用戶無權限匯入產品資料")
	}

	return fmt.Errorf("無效的用戶角色")
}

// getImportMessage 根據匯入結果生成訊息
func (h *Handler) getImportMessage(result *ServiceImportResult) string {
	if result.SuccessCount == 0 && result.FailureCount > 0 {
		return "匯入失敗，請檢查檔案格式和內容"
	}

	if result.FailureCount == 0 {
		return fmt.Sprintf("成功匯入 %d 筆資料", result.SuccessCount)
	}

	return fmt.Sprintf("匯入完成：成功 %d 筆，失敗 %d 筆",
		result.SuccessCount, result.FailureCount)
}

// handleServiceError 處理服務層錯誤並包含請求上下文
// 使用新的資料庫錯誤處理系統提供更精確的錯誤訊息
func (h *Handler) handleServiceError(w http.ResponseWriter, err error) {
	h.handleServiceErrorWithContext(context.Background(), w, err)
}

// handleServiceErrorWithContext 處理服務層錯誤並包含請求上下文
func (h *Handler) handleServiceErrorWithContext(ctx context.Context, w http.ResponseWriter, err error) {
	// 先檢查是否為資料庫錯誤，提供更精確的錯誤訊息
	errMsg := api.DBErrorMessage(err, "產品管理")
	if errMsg != "產品管理失敗" {
		// 成功獲得具體錯誤訊息，直接使用
		api.InternalErrorWithContext(ctx, w, errMsg)
		return
	}

	// 回退到通用錯誤處理
	api.HandleCommonServiceErrorWithContext(ctx, w, err, h.logger, "product service")
}
