package project

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"log/slog"
	"net/http"
	"net/http/httptest"
	"os"
	"strconv"
	"testing"
	"time"

	"github.com/koopa0/pms-api-v2/internal/business/auth"
	"github.com/koopa0/pms-api-v2/internal/constants"
	"github.com/koopa0/pms-api-v2/internal/platform/logger"
	"github.com/koopa0/pms-api-v2/internal/testutils"
	"github.com/koopa0/pms-api-v2/internal/types"
	"github.com/koopa0/pms-api-v2/internal/validator"
	"github.com/koopa0/pms-api-v2/sqlc"
)

// Following Architecture.md principle: test code defines interfaces based on what it needs
// Handler tests define interfaces for mocking service dependencies

// ProjectServiceInterface defines the service operations needed by the handler
type ProjectServiceInterface interface {
	CreateProject(ctx context.Context, req CreateProjectRequest, creatorID int32) (*Info, error)
	GetProjectByID(ctx context.Context, projectID int32) (*Info, error)
	ListProjects(ctx context.Context, req ListProjectsRequest) (*ListProjectsResponse, error)
}

// MockProjectService implements ProjectServiceInterface for testing
type MockProjectService struct {
	createResult     *Info
	createError      error
	project          *Info
	getError         error
	listResponse     *ListProjectsResponse
	listError        error
	shouldFailCreate bool
	shouldFailGet    bool
	shouldFailList   bool
}

func NewMockProjectService() *MockProjectService {
	return &MockProjectService{
		createResult: &Info{
			ID:        1,
			Name:      "Test Project",
			Type:      constants.ProjectTypes.General,
			Category:  constants.ProjectCategories.Hardware,
			Status:    constants.ProjectStatuses.Active,
			IsTest:    false,
			CreatedAt: types.Time(time.Now()),
			UpdatedAt: types.Time(time.Now()),
		},
		project: &Info{
			ID:        1,
			Name:      "Test Project",
			Type:      constants.ProjectTypes.General,
			Category:  constants.ProjectCategories.Hardware,
			Status:    constants.ProjectStatuses.Active,
			IsTest:    false,
			CreatedAt: types.Time(time.Now()),
			UpdatedAt: types.Time(time.Now()),
		},
		listResponse: &ListProjectsResponse{
			Data: []Info{
				{
					ID:        1,
					Name:      "Test Project",
					Type:      constants.ProjectTypes.General,
					Category:  constants.ProjectCategories.Hardware,
					Status:    constants.ProjectStatuses.Active,
					IsTest:    false,
					CreatedAt: types.Time(time.Now()),
					UpdatedAt: types.Time(time.Now()),
				},
			},
			Pagination: types.PaginationMeta{
				Page:        1,
				PageSize:    20,
				Total:       1,
				TotalPages:  1,
				HasNext:     false,
				HasPrevious: false,
			},
			Success: true,
		},
	}
}

func (m *MockProjectService) CreateProject(ctx context.Context, req CreateProjectRequest, creatorID int32) (*Info, error) {
	if m.shouldFailCreate {
		return nil, m.createError
	}
	// Update the result to match the request
	result := *m.createResult
	result.Name = req.Name
	result.Type = req.Type
	result.Category = req.Category
	result.Status = req.Status
	result.IsTest = req.IsTest
	return &result, nil
}

func (m *MockProjectService) GetProjectByID(ctx context.Context, projectID int32) (*Info, error) {
	if m.shouldFailGet {
		return nil, m.getError
	}
	// Update the project ID to match the request
	project := *m.project
	project.ID = projectID
	return &project, nil
}

func (m *MockProjectService) ListProjects(ctx context.Context, req ListProjectsRequest) (*ListProjectsResponse, error) {
	if m.shouldFailList {
		return nil, m.listError
	}
	return m.listResponse, nil
}

func TestNewHandler(t *testing.T) {
	t.Parallel()

	logger := slog.New(slog.NewTextHandler(os.Stdout, nil))
	validator := validator.NewValidator()

	handler := NewHandler(&Service{}, validator, logger)

	if handler == nil {
		t.Error("expected handler to be created")
	}
	if handler.logger != logger {
		t.Error("expected logger to be set")
	}
}

func TestHandler_CreateProject(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		requestBody    any
		setupMock      func(*MockProjectService)
		setupAuth      func(*http.Request)
		expectedStatus int
		expectError    bool
	}{
		{
			name: "successful project creation",
			requestBody: CreateProjectRequest{
				Name:     "Test Project",
				Type:     constants.ProjectTypes.General,
				Category: constants.ProjectCategories.Hardware,
				Status:   constants.ProjectStatuses.Active,
				IsTest:   false,
			},
			setupMock: func(m *MockProjectService) {
				// Default mock setup is sufficient
			},
			setupAuth: func(r *http.Request) {
				// Add auth context
				claims := &auth.Claims{
					UserID:   1,
					Email:    "<EMAIL>",
					Username: "testuser",
					Role:     constants.UserRoles.SPO,
				}
				ctx := context.WithValue(r.Context(), auth.ContextKeyUser, claims)
				*r = *r.WithContext(ctx)
			},
			expectedStatus: http.StatusOK,
			expectError:    false,
		},
		{
			name: "project creation with optional fields",
			requestBody: CreateProjectRequest{
				Name:                  "Advanced Project",
				Type:                  constants.ProjectTypes.Periodic,
				Category:              constants.ProjectCategories.Software,
				Status:                constants.ProjectStatuses.Active,
				IsTest:                false,
				Remarks:               "Detailed description with technical requirements",
				AttachmentSpace:       int64Ptr(1000000),
				ROCPriceReferenceYear: int32Ptr(112),
			},
			setupMock: func(m *MockProjectService) {
				// Default mock setup is sufficient
			},
			setupAuth: func(r *http.Request) {
				claims := &auth.Claims{
					UserID:   2,
					Email:    "<EMAIL>",
					Username: "cisa_user",
					Role:     constants.UserRoles.CISA,
				}
				ctx := context.WithValue(r.Context(), auth.ContextKeyUser, claims)
				*r = *r.WithContext(ctx)
			},
			expectedStatus: http.StatusOK,
			expectError:    false,
		},
		{
			name:           "invalid JSON",
			requestBody:    "invalid json",
			setupMock:      func(m *MockProjectService) {},
			setupAuth:      func(r *http.Request) {},
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
		},
		{
			name: "missing required fields",
			requestBody: CreateProjectRequest{
				Name: "Test Project",
				// Missing Type, Category, Status
			},
			setupMock: func(m *MockProjectService) {},
			setupAuth: func(r *http.Request) {
				claims := &auth.Claims{
					UserID:   1,
					Email:    "<EMAIL>",
					Username: "testuser",
					Role:     constants.UserRoles.SPO,
				}
				ctx := context.WithValue(r.Context(), auth.ContextKeyUser, claims)
				*r = *r.WithContext(ctx)
			},
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
		},
		{
			name: "service error",
			requestBody: CreateProjectRequest{
				Name:     "Test Project",
				Type:     constants.ProjectTypes.General,
				Category: constants.ProjectCategories.Hardware,
				Status:   constants.ProjectStatuses.Active,
				IsTest:   false,
			},
			setupMock: func(m *MockProjectService) {
				m.shouldFailCreate = true
				m.createError = ErrInternalError
			},
			setupAuth: func(r *http.Request) {
				claims := &auth.Claims{
					UserID:   1,
					Email:    "<EMAIL>",
					Username: "testuser",
					Role:     constants.UserRoles.SPO,
				}
				ctx := context.WithValue(r.Context(), auth.ContextKeyUser, claims)
				*r = *r.WithContext(ctx)
			},
			expectedStatus: http.StatusInternalServerError,
			expectError:    true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create test service
			mockService := NewMockProjectService()
			tt.setupMock(mockService)

			testService := &TestProjectService{service: mockService}
			logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}
			handler := &TestHandler{
				service: testService,
				logger:  logger,
			}

			// Create request body
			var body []byte
			var err error
			if str, ok := tt.requestBody.(string); ok {
				body = []byte(str)
			} else {
				body, err = json.Marshal(tt.requestBody)
				if err != nil {
					t.Fatalf("failed to marshal request body: %v", err)
				}
			}

			// Create request
			req := httptest.NewRequest(http.MethodPost, "/api/v1/projects", bytes.NewReader(body))
			req.Header.Set("Content-Type", "application/json")
			req = req.WithContext(testutils.CreateTestContext())

			// Setup auth
			tt.setupAuth(req)

			w := httptest.NewRecorder()

			// Call handler
			handler.CreateProject(w, req)

			// Check status code
			if w.Code != tt.expectedStatus {
				t.Errorf("expected status %d, got %d", tt.expectedStatus, w.Code)
			}

			// Check response for success cases
			if !tt.expectError {
				var response map[string]any
				if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
					t.Errorf("failed to parse response JSON: %v", err)
				}

				if response["success"] != true {
					t.Error("expected success to be true")
				}

				if response["data"] == nil {
					t.Error("expected data to be present")
				}
			}
		})
	}
}

func TestHandler_GetProject(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		projectID      string
		setupMock      func(*MockProjectService)
		expectedStatus int
		expectError    bool
	}{
		{
			name:      "successful get project",
			projectID: "1",
			setupMock: func(m *MockProjectService) {
				// Default mock setup is sufficient
			},
			expectedStatus: http.StatusOK,
			expectError:    false,
		},
		{
			name:           "invalid project ID",
			projectID:      "invalid",
			setupMock:      func(m *MockProjectService) {},
			expectedStatus: http.StatusBadRequest,
			expectError:    true,
		},
		{
			name:      "service error",
			projectID: "1",
			setupMock: func(m *MockProjectService) {
				m.shouldFailGet = true
				m.getError = ErrProjectNotFound
			},
			expectedStatus: http.StatusNotFound,
			expectError:    true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create test service
			mockService := NewMockProjectService()
			tt.setupMock(mockService)

			testService := &TestProjectService{service: mockService}
			logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}
			handler := &TestHandler{
				service: testService,
				logger:  logger,
			}

			// Create request
			req := httptest.NewRequest(http.MethodGet, "/api/v1/projects/"+tt.projectID, nil)
			req = req.WithContext(testutils.CreateTestContext())

			// Add the ID to the request context (simulating router behavior)
			if tt.projectID != "invalid" {
				req = req.WithContext(context.WithValue(req.Context(), "id", tt.projectID))
			}

			w := httptest.NewRecorder()

			// Call handler
			handler.GetProject(w, req)

			// Check status code
			if w.Code != tt.expectedStatus {
				t.Errorf("expected status %d, got %d", tt.expectedStatus, w.Code)
			}

			// Check response for success cases
			if !tt.expectError {
				var response map[string]any
				if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
					t.Errorf("failed to parse response JSON: %v", err)
				}

				if response["success"] != true {
					t.Error("expected success to be true")
				}

				if response["data"] == nil {
					t.Error("expected data to be present")
				}
			}
		})
	}
}

func TestHandler_ListProjects(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		queryParams    string
		setupMock      func(*MockProjectService)
		expectedStatus int
		expectError    bool
	}{
		{
			name:        "successful list projects",
			queryParams: "?page=1&page_size=20",
			setupMock: func(m *MockProjectService) {
				// Default mock setup is sufficient
			},
			expectedStatus: http.StatusOK,
			expectError:    false,
		},
		{
			name:        "list with filters",
			queryParams: "?status=進行中&type=一般詢價&category=電腦軟體雲端服務&page=1&page_size=10",
			setupMock: func(m *MockProjectService) {
				// Default mock setup is sufficient
			},
			expectedStatus: http.StatusOK,
			expectError:    false,
		},
		{
			name:        "service error",
			queryParams: "?page=1&page_size=20",
			setupMock: func(m *MockProjectService) {
				m.shouldFailList = true
				m.listError = ErrInternalError
			},
			expectedStatus: http.StatusInternalServerError,
			expectError:    true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create test service
			mockService := NewMockProjectService()
			tt.setupMock(mockService)

			testService := &TestProjectService{service: mockService}
			logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}
			handler := &TestHandler{
				service: testService,
				logger:  logger,
			}

			// Create request
			req := httptest.NewRequest(http.MethodGet, "/api/v1/projects"+tt.queryParams, nil)
			req = req.WithContext(testutils.CreateTestContext())

			w := httptest.NewRecorder()

			// Call handler
			handler.ListProjects(w, req)

			// Check status code
			if w.Code != tt.expectedStatus {
				t.Errorf("expected status %d, got %d", tt.expectedStatus, w.Code)
			}

			// Check response for success cases
			if !tt.expectError {
				var response map[string]any
				if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
					t.Errorf("failed to parse response JSON: %v", err)
				}

				if response["success"] != true {
					t.Error("expected success to be true")
				}

				if response["data"] == nil {
					t.Error("expected data to be present")
				}

				// Check pagination structure
				data, ok := response["data"].(map[string]any)
				if !ok {
					t.Error("expected data to be an object")
					return
				}

				if data["pagination"] == nil {
					t.Error("expected pagination to be present")
				}
			}
		})
	}
}

// TestProjectService wraps the mock service to implement the Service interface
type TestProjectService struct {
	service ProjectServiceInterface
}

func (t *TestProjectService) CreateProject(ctx context.Context, req CreateProjectRequest, creatorID int32) (*Info, error) {
	return t.service.CreateProject(ctx, req, creatorID)
}

func (t *TestProjectService) GetProjectByID(ctx context.Context, projectID int32) (*Info, error) {
	return t.service.GetProjectByID(ctx, projectID)
}

func (t *TestProjectService) ListProjects(ctx context.Context, req ListProjectsRequest) (*ListProjectsResponse, error) {
	return t.service.ListProjects(ctx, req)
}

// TestHandler wraps the test service to implement handler methods
type TestHandler struct {
	service ProjectServiceInterface
	logger  *logger.Logger
}

func (h *TestHandler) CreateProject(w http.ResponseWriter, r *http.Request) {
	var req CreateProjectRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "invalid JSON", http.StatusBadRequest)
		return
	}

	// Basic validation
	if req.Name == "" || req.Type == "" || req.Category == "" || req.Status == "" {
		http.Error(w, "missing required fields", http.StatusBadRequest)
		return
	}

	// Get user from context
	claims, ok := auth.GetUserFromContext(r.Context())
	if !ok || claims == nil {
		http.Error(w, "unauthorized", http.StatusUnauthorized)
		return
	}

	project, err := h.service.CreateProject(r.Context(), req, claims.UserID)
	if err != nil {
		if errors.Is(err, ErrInvalidInput) {
			http.Error(w, err.Error(), http.StatusBadRequest)
		} else {
			http.Error(w, err.Error(), http.StatusInternalServerError)
		}
		return
	}

	w.Header().Set("Content-Type", "application/json")
	if err = json.NewEncoder(w).Encode(map[string]any{
		"success": true,
		"data":    project,
	}); err != nil {
		return
	}
}

func (h *TestHandler) GetProject(w http.ResponseWriter, r *http.Request) {
	idStr := r.Context().Value("id")
	if idStr == nil {
		http.Error(w, "missing id parameter", http.StatusBadRequest)
		return
	}

	id, ok := idStr.(string)
	if !ok {
		http.Error(w, "invalid id parameter", http.StatusBadRequest)
		return
	}

	projectID, err := strconv.ParseInt(id, 10, 32)
	if err != nil {
		http.Error(w, "invalid id parameter", http.StatusBadRequest)
		return
	}

	project, err := h.service.GetProjectByID(r.Context(), int32(projectID))
	if err != nil {
		if errors.Is(err, ErrProjectNotFound) {
			http.Error(w, err.Error(), http.StatusNotFound)
		} else {
			http.Error(w, err.Error(), http.StatusInternalServerError)
		}
		return
	}

	w.Header().Set("Content-Type", "application/json")
	if err = json.NewEncoder(w).Encode(map[string]any{
		"success": true,
		"data":    project,
	}); err != nil {
		return
	}
}

func (h *TestHandler) ListProjects(w http.ResponseWriter, r *http.Request) {
	req := ListProjectsRequest{
		Pagination: types.PaginationParams{
			Page:     1,
			PageSize: 20,
		},
	}

	// Parse optional query parameters
	if status := r.URL.Query().Get("status"); status != "" {
		s := sqlc.ProjectStatus(status)
		req.Status = &s
	}
	if typeStr := r.URL.Query().Get("type"); typeStr != "" {
		t := sqlc.ProjectType(typeStr)
		req.Type = &t
	}
	if category := r.URL.Query().Get("category"); category != "" {
		c := sqlc.ProjectCategory(category)
		req.Category = &c
	}

	projects, err := h.service.ListProjects(r.Context(), req)
	if err != nil {
		if errors.Is(err, ErrInternalError) {
			http.Error(w, err.Error(), http.StatusInternalServerError)
		} else {
			http.Error(w, err.Error(), http.StatusBadRequest)
		}
		return
	}

	w.Header().Set("Content-Type", "application/json")
	if err = json.NewEncoder(w).Encode(map[string]any{
		"success": true,
		"data":    projects,
	}); err != nil {
		return
	}
}

// Helper functions for creating pointers
func int32Ptr(i int32) *int32 {
	return &i
}

func int64Ptr(i int64) *int64 {
	return &i
}
